<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证调试页面</title>
    
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔍 认证系统调试页面</h1>
    
    <div class="debug-container">
        <h2>实时状态监控</h2>
        <div id="status-display"></div>
        <button onclick="refreshStatus()">刷新状态</button>
        <button onclick="testPermissions()">测试权限</button>
        <button onclick="clearStorage()">清除存储</button>
    </div>

    <div class="debug-container">
        <h2>详细信息</h2>
        <div id="detail-display"></div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/supabase-config.js"></script>
    <script src="js/auth-manager.js"></script>
    
    <script>
        let statusInterval;
        
        function updateStatus() {
            const statusDiv = document.getElementById('status-display');
            const detailDiv = document.getElementById('detail-display');
            
            let html = '';
            let detailHtml = '';
            
            // 检查Supabase
            if (typeof window.supabase !== 'undefined') {
                html += '<div class="status success">✅ Supabase客户端已加载</div>';
            } else {
                html += '<div class="status error">❌ Supabase客户端未加载</div>';
            }
            
            // 检查AuthManager
            if (typeof window.authManager !== 'undefined' && window.authManager !== null) {
                html += '<div class="status success">✅ AuthManager已初始化</div>';
                
                const currentUser = window.authManager.getCurrentUser();
                const userType = window.authManager.getUserType();
                
                if (currentUser) {
                    html += `<div class="status success">✅ 用户已登录: ${currentUser.email}</div>`;
                    html += `<div class="status info">ℹ️ 用户类型: ${userType}</div>`;
                    
                    detailHtml += '<h3>用户详细信息</h3>';
                    detailHtml += `<pre>${JSON.stringify(currentUser, null, 2)}</pre>`;
                } else {
                    html += '<div class="status warning">⚠️ 用户未登录</div>';
                    html += `<div class="status info">ℹ️ 用户类型: ${userType}</div>`;
                }
                
                // 权限检查
                const canView = window.authManager.canViewDetails();
                const canDownload = window.authManager.canDownloadBasic();
                const isAdmin = window.authManager.isAdmin();
                
                html += `<div class="status ${canView ? 'success' : 'error'}">${canView ? '✅' : '❌'} 可查看详情: ${canView}</div>`;
                html += `<div class="status ${canDownload ? 'success' : 'error'}">${canDownload ? '✅' : '❌'} 可下载基础资料: ${canDownload}</div>`;
                html += `<div class="status ${isAdmin ? 'success' : 'info'}">${isAdmin ? '✅' : 'ℹ️'} 管理员权限: ${isAdmin}</div>`;
                
            } else {
                html += '<div class="status error">❌ AuthManager未初始化</div>';
            }
            
            // 检查全局函数
            if (typeof window.canViewDetails === 'function') {
                const globalCanView = window.canViewDetails();
                html += `<div class="status ${globalCanView ? 'success' : 'error'}">${globalCanView ? '✅' : '❌'} 全局canViewDetails(): ${globalCanView}</div>`;
            } else {
                html += '<div class="status error">❌ 全局canViewDetails函数未定义</div>';
            }
            
            // 检查全局变量
            detailHtml += '<h3>全局变量状态</h3>';
            detailHtml += `<pre>currentUser: ${typeof window.currentUser !== 'undefined' ? JSON.stringify(window.currentUser, null, 2) : 'undefined'}</pre>`;
            detailHtml += `<pre>currentUserType: ${window.currentUserType || 'undefined'}</pre>`;
            
            statusDiv.innerHTML = html;
            detailDiv.innerHTML = detailHtml;
        }
        
        function refreshStatus() {
            updateStatus();
        }
        
        function testPermissions() {
            console.log('=== 权限测试开始 ===');
            
            if (window.authManager) {
                console.log('AuthManager存在:', window.authManager);
                console.log('当前用户:', window.authManager.getCurrentUser());
                console.log('用户类型:', window.authManager.getUserType());
                console.log('可查看详情:', window.authManager.canViewDetails());
                console.log('可下载基础:', window.authManager.canDownloadBasic());
                console.log('是管理员:', window.authManager.isAdmin());
            }
            
            if (typeof window.canViewDetails === 'function') {
                console.log('全局canViewDetails():', window.canViewDetails());
            }
            
            console.log('=== 权限测试结束 ===');
            updateStatus();
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            alert('存储已清除，请刷新页面');
        }
        
        // 页面加载完成后开始监控
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试页面加载完成');
            
            // 立即更新一次状态
            setTimeout(updateStatus, 500);
            
            // 每2秒更新一次状态
            statusInterval = setInterval(updateStatus, 2000);
            
            // 监听认证状态变化
            if (typeof window.supabase !== 'undefined') {
                window.supabase.auth.onAuthStateChange((event, session) => {
                    console.log('认证状态变化:', event, session);
                    setTimeout(updateStatus, 500);
                });
            }
        });
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
        });
    </script>
</body>
</html>
