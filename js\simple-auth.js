// 简化认证系统 - 只使用 public.users 表
class SimpleAuth {
    constructor() {
        this.currentUser = null;
        this.currentUserType = 'guest';
        this.supabase = null;
        this.initSupabase();
        console.log('🔐 [SIMPLE-AUTH] 简化认证系统初始化');
    }

    // 初始化Supabase客户端
    initSupabase() {
        // 等待Supabase配置加载
        const checkSupabase = () => {
            if (window.supabaseClient) {
                this.supabase = window.supabaseClient;
                console.log('🔐 [SIMPLE-AUTH] Supabase客户端已连接');
            } else if (window.supabase && window.supabase.createClient) {
                // 如果全局supabase可用，创建客户端
                const SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';
                this.supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                console.log('🔐 [SIMPLE-AUTH] 创建新的Supabase客户端');
            } else {
                console.log('🔐 [SIMPLE-AUTH] 等待Supabase加载...');
                setTimeout(checkSupabase, 500);
            }
        };
        checkSupabase();
    }

    // 密码哈希函数（简单版本）
    hashPassword(password) {
        // 使用简单的哈希方法，实际项目中应该使用更安全的方法
        return btoa(password + 'chunsheng_salt'); // base64编码 + 盐值
    }

    // 验证密码
    verifyPassword(password, hashedPassword) {
        return this.hashPassword(password) === hashedPassword;
    }

    // 用户登录
    async loginUser(email, password) {
        try {
            console.log('🔐 [SIMPLE-AUTH] 尝试登录:', email);

            // 检查Supabase客户端是否可用
            if (!this.supabase) {
                return { success: false, message: 'Supabase客户端未初始化，请稍后重试' };
            }

            // 从 public.users 表查询用户
            const { data: user, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('email', email)
                .eq('is_active', true)
                .single();

            if (error) {
                console.error('🔐 [SIMPLE-AUTH] 查询用户失败:', error);
                return { success: false, message: '用户不存在或已被禁用' };
            }

            if (!user) {
                return { success: false, message: '用户不存在或已被禁用' };
            }

            // 验证密码
            if (!user.password_hash) {
                return { success: false, message: '用户密码未设置，请联系管理员' };
            }

            if (!this.verifyPassword(password, user.password_hash)) {
                return { success: false, message: '密码错误' };
            }

            // 登录成功，设置当前用户
            this.currentUser = user;
            this.currentUserType = user.user_type || 'premium';

            // 保存到本地存储
            localStorage.setItem('simple_auth_user', JSON.stringify(user));
            localStorage.setItem('simple_auth_login_time', Date.now().toString());

            // 更新UI
            this.updateUI();

            console.log('🔐 [SIMPLE-AUTH] 登录成功:', user.username);
            return {
                success: true,
                message: '登录成功！',
                user: user
            };

        } catch (error) {
            console.error('🔐 [SIMPLE-AUTH] 登录过程出错:', error);
            return { success: false, message: '登录失败，请稍后重试' };
        }
    }

    // 用户注册
    async registerUser(userData) {
        try {
            console.log('🔐 [SIMPLE-AUTH] 尝试注册:', userData.email);

            // 检查用户是否已存在
            const { data: existingUser } = await this.supabase
                .from('users')
                .select('email')
                .eq('email', userData.email)
                .single();

            if (existingUser) {
                return { success: false, message: '该邮箱已被注册' };
            }

            // 创建新用户
            const newUser = {
                username: userData.username,
                email: userData.email,
                password_hash: this.hashPassword(userData.password),
                user_type: userData.user_type || 'premium',
                company_name: userData.company_name || '',
                phone: userData.phone || '',
                first_name: userData.first_name || '',
                last_name: userData.last_name || '',
                is_active: true
            };

            const { data: user, error } = await this.supabase
                .from('users')
                .insert([newUser])
                .select()
                .single();

            if (error) {
                console.error('🔐 [SIMPLE-AUTH] 注册失败:', error);
                return { success: false, message: '注册失败: ' + error.message };
            }

            console.log('🔐 [SIMPLE-AUTH] 注册成功:', user.username);
            return {
                success: true,
                message: '注册成功！',
                user: user
            };

        } catch (error) {
            console.error('🔐 [SIMPLE-AUTH] 注册过程出错:', error);
            return { success: false, message: '注册失败，请稍后重试' };
        }
    }

    // 修改密码
    async changePassword(userId, oldPassword, newPassword) {
        try {
            // 验证旧密码
            const { data: user, error } = await this.supabase
                .from('users')
                .select('password_hash')
                .eq('id', userId)
                .single();

            if (error || !user) {
                return { success: false, message: '用户不存在' };
            }

            if (!this.verifyPassword(oldPassword, user.password_hash)) {
                return { success: false, message: '原密码错误' };
            }

            // 更新密码
            const { error: updateError } = await this.supabase
                .from('users')
                .update({ password_hash: this.hashPassword(newPassword) })
                .eq('id', userId);

            if (updateError) {
                return { success: false, message: '密码更新失败' };
            }

            return { success: true, message: '密码修改成功' };

        } catch (error) {
            console.error('🔐 [SIMPLE-AUTH] 修改密码失败:', error);
            return { success: false, message: '修改密码失败' };
        }
    }

    // 管理员重置用户密码
    async adminResetPassword(userId, newPassword) {
        try {
            if (!this.isAdmin()) {
                return { success: false, message: '权限不足' };
            }

            const { error } = await this.supabase
                .from('users')
                .update({ password_hash: this.hashPassword(newPassword) })
                .eq('id', userId);

            if (error) {
                return { success: false, message: '密码重置失败: ' + error.message };
            }

            return { success: true, message: '密码重置成功' };

        } catch (error) {
            console.error('🔐 [SIMPLE-AUTH] 管理员重置密码失败:', error);
            return { success: false, message: '密码重置失败' };
        }
    }

    // 登出
    logout() {
        this.currentUser = null;
        this.currentUserType = 'guest';
        localStorage.removeItem('simple_auth_user');
        localStorage.removeItem('simple_auth_login_time');
        this.updateUI();
        console.log('🔐 [SIMPLE-AUTH] 用户已登出');
        return { success: true, message: '登出成功' };
    }

    // 检查登录状态
    checkLoginStatus() {
        const savedUser = localStorage.getItem('simple_auth_user');
        const loginTime = localStorage.getItem('simple_auth_login_time');

        if (savedUser && loginTime) {
            // 检查登录是否过期（24小时）
            const now = Date.now();
            const loginTimestamp = parseInt(loginTime);
            const expireTime = 24 * 60 * 60 * 1000; // 24小时

            if (now - loginTimestamp < expireTime) {
                this.currentUser = JSON.parse(savedUser);
                this.currentUserType = this.currentUser.user_type || 'premium';
                this.updateUI();
                console.log('🔐 [SIMPLE-AUTH] 恢复登录状态:', this.currentUser.username);
                return true;
            } else {
                // 登录已过期
                this.logout();
            }
        }
        return false;
    }

    // 权限检查
    isAdmin() {
        return this.currentUserType === 'admin';
    }

    canViewDetails() {
        return ['premium', 'privileged', 'admin'].includes(this.currentUserType);
    }

    canDownload() {
        return ['privileged', 'admin'].includes(this.currentUserType);
    }

    canDownloadBasic() {
        return ['premium', 'privileged', 'admin'].includes(this.currentUserType);
    }

    // 获取当前用户
    getCurrentUser() {
        return this.currentUser;
    }

    getUserType() {
        return this.currentUserType;
    }

    // 更新UI
    updateUI() {
        const loginBtn = document.querySelector('.login-btn');
        const registerBtn = document.querySelector('.register-btn');
        
        if (this.currentUser) {
            if (loginBtn) {
                loginBtn.textContent = this.currentUser.username;
                loginBtn.href = '#';
                loginBtn.onclick = () => this.showUserMenu();
            }
            if (registerBtn) {
                registerBtn.textContent = '退出';
                registerBtn.onclick = () => this.logout();
            }
        } else {
            if (loginBtn) {
                loginBtn.textContent = '登录';
                loginBtn.href = 'login.html';
                loginBtn.onclick = null;
            }
            if (registerBtn) {
                registerBtn.textContent = '注册';
                registerBtn.onclick = null;
            }
        }
    }

    showUserMenu() {
        if (this.currentUser) {
            alert(`用户：${this.currentUser.username}\n权限：${this.getUserTypeDisplay()}\n公司：${this.currentUser.company_name || '未填写'}`);
        }
    }

    getUserTypeDisplay() {
        switch(this.currentUserType) {
            case 'guest': return '游客';
            case 'premium': return '高级用户';
            case 'privileged': return '特许用户';
            case 'admin': return '管理员';
            default: return '未知';
        }
    }
}

// 创建全局实例
window.simpleAuth = new SimpleAuth();

// 兼容性函数
window.loginUser = (email, password) => window.simpleAuth.loginUser(email, password);
window.registerUser = (userData) => window.simpleAuth.registerUser(userData);
window.logoutUser = () => window.simpleAuth.logout();
window.getCurrentUserInfo = () => window.simpleAuth.getCurrentUser();
window.checkAdminAuth = () => window.simpleAuth.isAdmin();

// 权限检查全局函数
window.canViewDetails = () => window.simpleAuth.canViewDetails();
window.canDownload = () => window.simpleAuth.canDownload();
window.canDownloadBasic = () => window.simpleAuth.canDownloadBasic();

// 页面加载时检查登录状态
document.addEventListener('DOMContentLoaded', function() {
    window.simpleAuth.checkLoginStatus();
});

console.log('🔐 [SIMPLE-AUTH] 简化认证系统加载完成');
