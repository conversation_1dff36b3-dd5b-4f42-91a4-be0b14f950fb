<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API调试工具</h1>
        
        <button onclick="testDirectAPI()">测试直接API</button>
        <button onclick="testSimulatedAPI()">测试模拟API</button>
        <button onclick="testBothAPIs()">对比测试</button>
        
        <div id="results"></div>
    </div>

    <script>
        const SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';

        function addResult(title, data) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'result';
            resultDiv.innerHTML = `<strong>${title}</strong>\n${JSON.stringify(data, null, 2)}`;
            resultsDiv.appendChild(resultDiv);
        }

        // 直接API调用
        async function testDirectAPI() {
            try {
                const params = new URLSearchParams();
                params.append('select', '*');
                params.append('email', '<EMAIL>');
                params.append('is_active', 'eq.true');
                params.append('limit', '1');

                const response = await fetch(`${SUPABASE_URL}/rest/v1/users?${params.toString()}`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                addResult('直接API调用结果', {
                    status: response.status,
                    data: data,
                    user: data[0] || null
                });

            } catch (error) {
                addResult('直接API调用错误', error.message);
            }
        }

        // 模拟API调用（登录页面使用的方式）
        async function testSimulatedAPI() {
            try {
                // 复制登录页面的API实现
                const client = {
                    from: function(table) {
                        return {
                            select: function(columns) {
                                const query = {
                                    table: table,
                                    columns: columns,
                                    conditions: [],
                                    url: SUPABASE_URL,
                                    key: SUPABASE_ANON_KEY
                                };
                                
                                return {
                                    eq: function(column, value) {
                                        query.conditions.push({ column, operator: 'eq', value });
                                        return {
                                            eq: function(column2, value2) {
                                                query.conditions.push({ column: column2, operator: 'eq', value: value2 });
                                                return {
                                                    single: async function() {
                                                        return await executeQuery(query, true);
                                                    }
                                                };
                                            }
                                        };
                                    }
                                };
                            }
                        };
                    }
                };

                async function executeQuery(query, single = false) {
                    try {
                        const params = new URLSearchParams();
                        params.append('select', query.columns);
                        
                        query.conditions.forEach(condition => {
                            params.append(condition.column, `${condition.operator}.${condition.value}`);
                        });
                        
                        if (single) {
                            params.append('limit', '1');
                        }
                        
                        const response = await fetch(`${query.url}/rest/v1/${query.table}?${params.toString()}`, {
                            headers: {
                                'apikey': query.key,
                                'Authorization': `Bearer ${query.key}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        return { 
                            data: single ? (data[0] || null) : data, 
                            error: null 
                        };
                    } catch (error) {
                        return { 
                            data: null, 
                            error: error 
                        };
                    }
                }

                const result = await client
                    .from('users')
                    .select('*')
                    .eq('email', '<EMAIL>')
                    .eq('is_active', true)
                    .single();

                addResult('模拟API调用结果', result);

            } catch (error) {
                addResult('模拟API调用错误', error.message);
            }
        }

        async function testBothAPIs() {
            addResult('=== 开始对比测试 ===', '');
            await testDirectAPI();
            await testSimulatedAPI();
            addResult('=== 对比测试完成 ===', '');
        }
    </script>
</body>
</html>
