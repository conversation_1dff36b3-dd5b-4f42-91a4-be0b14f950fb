// 统一认证管理器 - 兼容前端用户和管理员后台
class AuthManager {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.userType = 'guest'; // guest, premium, privileged, admin
        this.isLegacyAdmin = false; // 是否使用传统管理员登录

        // 三级权限系统定义
        this.USER_TYPES = {
            GUEST: 'guest',           // 游客 - 基本浏览权限
            PREMIUM: 'premium',       // 高级用户 - 注册用户，可查看详情
            PRIVILEGED: 'privileged', // 特许用户 - 可下载部分资料
            ADMIN: 'admin'            // 管理员 - 完全权限
        };
        this.init();
    }

    async init() {
        console.log('🔐 [AUTH-MANAGER] 开始初始化认证管理器');
        console.log('🔐 [AUTH-MANAGER] window.supabase存在:', typeof window.supabase !== 'undefined');
        
        // 等待全局Supabase客户端初始化
        let retryCount = 0;
        while (retryCount < 20) {
            // 检查supabaseClient（实际的客户端实例）
            if (typeof window.supabaseClient !== 'undefined' && window.supabaseClient && typeof window.supabaseClient.auth !== 'undefined') {
                console.log('🔐 [AUTH-MANAGER] 找到全局Supabase客户端实例');
                this.supabase = window.supabaseClient;
                console.log('🔐 [AUTH-MANAGER] Supabase客户端已设置');
                await this.checkSupabaseAuth();
                break;
            } else {
                console.log(`🔐 [AUTH-MANAGER] 等待Supabase初始化... (${retryCount + 1}/20)`);
                console.log('  - window.supabase存在:', typeof window.supabase !== 'undefined');
                console.log('  - window.supabaseClient存在:', typeof window.supabaseClient !== 'undefined');
                await new Promise(resolve => setTimeout(resolve, 200));
                retryCount++;
            }
        }

        if (retryCount >= 20) {
            console.warn('🔐 [AUTH-MANAGER] 全局supabase客户端初始化超时，跳过Supabase初始化');
        }
        
        // 检查传统管理员登录
        console.log('🔐 [AUTH-MANAGER] 检查传统管理员登录');
        this.checkLegacyAdminAuth();
        
        // 设置认证状态监听
        console.log('🔐 [AUTH-MANAGER] 设置认证状态监听');
        this.setupAuthListeners();
        
        console.log('🔐 [AUTH-MANAGER] 认证管理器初始化完成');
        console.log('🔐 [AUTH-MANAGER] 最终状态:', {
            currentUser: this.currentUser,
            userType: this.userType,
            isLegacyAdmin: this.isLegacyAdmin
        });
    }

    // 检查简化认证状态
    async checkSupabaseAuth() {
        console.log('🔐 [AUTH-MANAGER] 开始检查简化认证状态');

        try {
            // 从本地存储检查登录状态
            const savedUser = localStorage.getItem('simple_auth_user');
            const loginTime = localStorage.getItem('simple_auth_login_time');

            if (savedUser && loginTime) {
                // 检查登录是否过期（24小时）
                const now = Date.now();
                const loginTimestamp = parseInt(loginTime);
                const expireTime = 24 * 60 * 60 * 1000; // 24小时

                if (now - loginTimestamp < expireTime) {
                    const user = JSON.parse(savedUser);
                    console.log('🔐 [AUTH-MANAGER] 恢复简化认证状态:', user.username);

                    this.currentUser = user;
                    this.userType = user.user_type || this.USER_TYPES.PREMIUM;
                    this.isLegacyAdmin = false;
                    console.log('🔐 [AUTH-MANAGER] 简化认证用户已登录:', user.username);
                    console.log('🔐 [AUTH-MANAGER] 用户权限类型:', this.userType);

                    // 同步到全局变量（兼容旧代码）
                    window.currentUser = user;
                    window.currentUserType = this.userType;
                    console.log('🔐 [AUTH-MANAGER] 全局变量已同步:', {
                        currentUser: window.currentUser,
                        currentUserType: window.currentUserType
                    });

                    this.updateUIForUser();
                    console.log('🔐 [AUTH-MANAGER] 简化认证检查成功');
                    return true;
                } else {
                    // 登录已过期，清除存储
                    localStorage.removeItem('simple_auth_user');
                    localStorage.removeItem('simple_auth_login_time');
                    console.log('🔐 [AUTH-MANAGER] 登录已过期，已清除');
                }
            } else {
                console.log('🔐 [AUTH-MANAGER] 没有有效的登录状态');
            }
        } catch (error) {
            console.error('🔐 [AUTH-MANAGER] 检查Supabase认证失败:', error);
        }
        
        console.log('🔐 [AUTH-MANAGER] Supabase认证检查完成，未找到有效用户');
        return false;
    }

    // 检查传统管理员认证
    checkLegacyAdminAuth() {
        const isAdminLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
        if (isAdminLoggedIn) {
            this.currentUser = {
                id: localStorage.getItem('admin_user_id') || 'legacy-admin',
                username: localStorage.getItem('admin_username') || 'admin',
                email: localStorage.getItem('admin_email') || '<EMAIL>',
                user_type: this.USER_TYPES.ADMIN
            };
            this.userType = this.USER_TYPES.ADMIN;
            this.isLegacyAdmin = true;
            console.log('传统管理员已登录:', this.currentUser);
            console.log('管理员权限类型:', this.userType);

            // 同步到全局变量（兼容旧代码）
            window.currentUser = this.currentUser;
            window.currentUserType = this.userType;
            console.log('传统管理员全局变量已同步:', { currentUser: window.currentUser, currentUserType: window.currentUserType });

            this.updateUIForUser();
            return true;
        }
        return false;
    }

    // 设置认证监听器
    setupAuthListeners() {
        if (this.supabase) {
            this.supabase.auth.onAuthStateChange((event, session) => {
                console.log('认证状态变化:', event, session);
                if (event === 'SIGNED_IN') {
                    this.checkSupabaseAuth();
                } else if (event === 'SIGNED_OUT') {
                    if (!this.isLegacyAdmin) {
                        this.logout();
                    }
                }
            });
        }
    }

    // 前端用户登录 - 使用简化认证
    async loginUser(email, password) {
        console.log('🔐 [AUTH-MANAGER] 开始登录流程:', email);

        if (!this.supabase) {
            console.error('🔐 [AUTH-MANAGER] Supabase客户端未初始化');
            return { success: false, message: 'Supabase未初始化，请刷新页面重试' };
        }

        try {
            console.log('🔐 [AUTH-MANAGER] 使用简化认证系统登录:', email);

            // 查询用户
            const { data: user, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('email', email)
                .eq('is_active', true)
                .single();

            console.log('🔐 [AUTH-MANAGER] 用户查询结果:', { user, error });

            if (error) {
                console.error('🔐 [AUTH-MANAGER] 用户查询失败:', error);
                if (error.code === 'PGRST116') {
                    return { success: false, message: '用户不存在或已被禁用' };
                }
                return { success: false, message: '查询用户失败: ' + error.message };
            }

            if (!user) {
                return { success: false, message: '用户不存在或已被禁用' };
            }

            if (!user.password_hash) {
                return { success: false, message: '用户密码未设置，请联系管理员' };
            }

            // 验证密码
            const hashedPassword = btoa(password + 'chunsheng_salt');
            console.log('🔐 [AUTH-MANAGER] 密码验证:', {
                inputHash: hashedPassword,
                storedHash: user.password_hash,
                match: hashedPassword === user.password_hash
            });

            if (hashedPassword !== user.password_hash) {
                return { success: false, message: '密码错误' };
            }

            // 登录成功，设置当前用户
            this.currentUser = user;
            this.userType = user.user_type || 'premium';

            // 保存到本地存储
            localStorage.setItem('simple_auth_user', JSON.stringify(user));
            localStorage.setItem('simple_auth_login_time', Date.now().toString());

            // 同步到全局变量（兼容旧代码）
            window.currentUser = user;
            window.currentUserType = this.userType;

            // 更新UI
            this.updateUIForUser();

            console.log('🔐 [AUTH-MANAGER] 简化认证登录成功:', user.username);
            return {
                success: true,
                message: '登录成功！',
                user: user
            };

        } catch (error) {
            console.error('🔐 [AUTH-MANAGER] 登录过程出错:', error);
            return { success: false, message: '登录失败，请稍后重试: ' + error.message };
        }
    }

    // 前端用户注册
    async registerUser(userData) {
        if (!this.supabase) {
            return { success: false, message: 'Supabase未初始化' };
        }

        try {
            // 注册用户到 Supabase Auth
            const { data: authData, error: authError } = await this.supabase.auth.signUp({
                email: userData.email,
                password: userData.password,
                options: {
                    data: {
                        first_name: userData.firstName,
                        last_name: userData.lastName,
                        phone: userData.phone,
                        company: userData.company
                    }
                }
            });

            if (authError) {
                console.error('Auth 注册失败:', authError);
                return { success: false, message: authError.message };
            }

            // 使用数据库函数创建用户记录
            const { data: userInsertData, error: userInsertError } = await this.supabase
                .rpc('create_user_profile', {
                    user_id: authData.user.id,
                    user_email: userData.email,
                    user_username: `${userData.firstName}${userData.lastName}`,
                    user_first_name: userData.firstName,
                    user_last_name: userData.lastName,
                    user_phone: userData.phone,
                    user_company_name: userData.company,
                    user_type: userData.userType || 'user'
                });

            if (userInsertError) {
                console.error('用户记录创建失败:', userInsertError);
                return { success: false, message: '用户记录创建失败' };
            }

            // 检查函数返回的结果
            if (userInsertData && userInsertData.error) {
                console.error('用户记录创建失败:', userInsertData.error);
                return { success: false, message: '用户记录创建失败: ' + userInsertData.error };
            }

            console.log('用户注册成功:', userInsertData);

            // 根据用户类型返回不同的消息
            let message = '';
            switch (userData.userType) {
                case this.USER_TYPES.PREMIUM:
                    message = '注册成功！您已获得高级用户权限，可以查看产品详情。';
                    break;
                case this.USER_TYPES.PRIVILEGED:
                    message = '注册成功！特许用户权限需要管理员审核，请等待邮件通知。';
                    break;
                case this.USER_TYPES.ADMIN:
                    message = '注册成功！管理员权限需要审核，请等待邮件通知。';
                    break;
                default:
                    message = '注册成功！';
            }

            return {
                success: true,
                message: message,
                user: userInsertData
            };

        } catch (error) {
            console.error('注册过程出错:', error);
            return { success: false, message: '注册失败，请稍后重试' };
        }
    }

    // 传统管理员登录（保留旧版形式）
    async loginAdmin(username, password) {
        // 预设的管理员账户（保留旧版逻辑）
        const adminAccounts = [
            { username: 'admin', password: 'admin123', name: '系统管理员' },
            { username: 'chunsheng', password: 'cs2024', name: '春晟管理员' }
        ];

        const admin = adminAccounts.find(acc => 
            acc.username === username && acc.password === password
        );

        if (admin) {
            // 设置传统管理员登录状态
            localStorage.setItem('admin_logged_in', 'true');
            localStorage.setItem('admin_username', admin.name);
            localStorage.setItem('admin_user_id', 'legacy-' + admin.username);
            localStorage.setItem('admin_email', admin.username + '@chunsheng.com');
            localStorage.setItem('admin_login_time', new Date().toISOString());

            this.currentUser = {
                id: 'legacy-' + admin.username,
                username: admin.name,
                email: admin.username + '@chunsheng.com',
                user_type: 'admin'
            };
            this.userType = 'admin';
            this.isLegacyAdmin = true;

            return { success: true, message: '管理员登录成功！', user: this.currentUser };
        } else {
            return { success: false, message: '用户名或密码错误' };
        }
    }

    // 统一登出
    async logout() {
        try {
            console.log('🔐 [AUTH-MANAGER] 开始登出流程');

            // 清除简化认证
            localStorage.removeItem('simple_auth_user');
            localStorage.removeItem('simple_auth_login_time');

            // 清除Supabase认证
            if (this.supabase && !this.isLegacyAdmin) {
                await this.supabase.auth.signOut();
            }

            // 清除传统管理员登录
            if (this.isLegacyAdmin) {
                localStorage.removeItem('admin_logged_in');
                localStorage.removeItem('admin_username');
                localStorage.removeItem('admin_user_id');
                localStorage.removeItem('admin_email');
                localStorage.removeItem('admin_login_time');
            }

            // 重置状态
            this.currentUser = null;
            this.userType = 'guest';
            this.isLegacyAdmin = false;

            // 同步到全局变量
            window.currentUser = null;
            window.currentUserType = 'guest';

            // 更新UI
            this.updateUIForUser();

            console.log('🔐 [AUTH-MANAGER] 登出成功');
            return { success: true, message: '登出成功！' };

        } catch (error) {
            console.error('🔐 [AUTH-MANAGER] 登出过程出错:', error);
            return { success: false, message: '登出失败，请稍后重试' };
        }
    }

    // 获取当前用户信息
    getCurrentUser() {
        return this.currentUser;
    }

    // 获取用户类型
    getUserType() {
        return this.userType;
    }

    // 检查是否已登录
    isLoggedIn() {
        return this.currentUser !== null;
    }

    // 检查是否是管理员
    isAdmin() {
        return this.userType === 'admin';
    }

    // 检查是否是传统管理员
    isLegacyAdminUser() {
        return this.isLegacyAdmin;
    }

    // 三级权限检查系统
    canViewDetails() {
        // 高级用户及以上可以查看详情
        return [this.USER_TYPES.PREMIUM, this.USER_TYPES.PRIVILEGED, this.USER_TYPES.ADMIN].includes(this.userType);
    }

    canDownloadBasic() {
        // 特许用户及以上可以下载基础资料
        return [this.USER_TYPES.PRIVILEGED, this.USER_TYPES.ADMIN].includes(this.userType);
    }

    canDownloadAll() {
        // 只有管理员可以下载所有资料
        return this.userType === this.USER_TYPES.ADMIN;
    }

    canManageUsers() {
        return this.userType === this.USER_TYPES.ADMIN;
    }

    canManageProducts() {
        return this.userType === this.USER_TYPES.ADMIN;
    }

    canAccessAdmin() {
        return this.userType === this.USER_TYPES.ADMIN;
    }

    // 获取用户权限显示名称
    getUserTypeDisplay() {
        const typeNames = {
            [this.USER_TYPES.GUEST]: '游客',
            [this.USER_TYPES.PREMIUM]: '高级用户',
            [this.USER_TYPES.PRIVILEGED]: '特许用户',
            [this.USER_TYPES.ADMIN]: '管理员'
        };
        return typeNames[this.userType] || '未知';
    }

    // 获取用户权限描述
    getUserPermissionDescription() {
        const descriptions = {
            [this.USER_TYPES.GUEST]: '可浏览产品信息，发送客服消息',
            [this.USER_TYPES.PREMIUM]: '可查看产品详情，管理个人信息',
            [this.USER_TYPES.PRIVILEGED]: '可下载部分技术资料，查看详细规格',
            [this.USER_TYPES.ADMIN]: '完全管理权限，可管理用户和产品'
        };
        return descriptions[this.userType] || '权限未定义';
    }

    // 更新UI显示
    updateUIForUser() {
        // 更新用户状态显示
        const userStatusElements = document.querySelectorAll('.user-status, #user-status');
        userStatusElements.forEach(element => {
            if (element) {
                element.textContent = `当前状态：${this.getUserTypeDisplay()}`;
                element.className = `user-status user-${this.userType}`;
            }
        });

        // 更新用户名显示
        const usernameElements = document.querySelectorAll('.username, #username, #admin-username');
        usernameElements.forEach(element => {
            if (element && this.currentUser) {
                element.textContent = this.currentUser.username || this.currentUser.email;
            }
        });

        // 显示/隐藏功能按钮
        this.updateFeatureVisibility();
    }

    // 更新功能可见性
    updateFeatureVisibility() {
        // 详情查看按钮
        const detailButtons = document.querySelectorAll('.view-details-btn, .product-details');
        detailButtons.forEach(btn => {
            btn.style.display = this.canViewDetails() ? 'block' : 'none';
        });

        // 下载按钮
        const downloadButtons = document.querySelectorAll('.download-btn');
        downloadButtons.forEach(btn => {
            if (btn.dataset.level === 'basic') {
                btn.style.display = this.canDownloadBasic() ? 'block' : 'none';
            } else {
                btn.style.display = this.canDownloadAll() ? 'block' : 'none';
            }
        });

        // 管理员功能
        const adminElements = document.querySelectorAll('.admin-only, .admin-panel');
        adminElements.forEach(element => {
            element.style.display = this.canAccessAdmin() ? 'block' : 'none';
        });

        // 权限提示
        this.showPermissionHints();
    }

    // 显示权限提示
    showPermissionHints() {
        const hintElement = document.getElementById('permission-hint');
        if (hintElement) {
            hintElement.innerHTML = `
                <div class="permission-info">
                    <strong>${this.getUserTypeDisplay()}</strong>
                    <p>${this.getUserPermissionDescription()}</p>
                </div>
            `;
        }
    }
}

// 创建全局认证管理器实例
window.authManager = new AuthManager();

// 兼容性函数（保持旧版API）
window.loginUser = (email, password) => window.authManager.loginUser(email, password);
window.registerUser = (userData) => window.authManager.registerUser(userData);
window.logoutUser = () => window.authManager.logout();
window.getCurrentUserInfo = () => window.authManager.checkSupabaseAuth();
window.checkAdminAuth = () => window.authManager.isAdmin();

// 权限检查全局函数（兼容旧版代码）
console.log('🔐 [AUTH-MANAGER] 设置全局权限检查函数');
window.canViewDetails = () => {
    const result = window.authManager ? window.authManager.canViewDetails() : false;
    console.log('🔐 [GLOBAL] canViewDetails() 被调用，结果:', result);
    return result;
};
window.canDownload = () => {
    const result = window.authManager.canDownloadBasic(); // 特许用户可下载
    console.log('🔐 [GLOBAL] canDownload() 被调用，结果:', result);
    return result;
};
window.canDownloadBasic = () => {
    const result = window.authManager.canDownloadBasic();
    console.log('🔐 [GLOBAL] canDownloadBasic() 被调用，结果:', result);
    return result;
};
window.canDownloadAll = () => {
    const result = window.authManager.canDownloadAll();
    console.log('🔐 [GLOBAL] canDownloadAll() 被调用，结果:', result);
    return result;
};
console.log('🔐 [AUTH-MANAGER] 全局权限检查函数设置完成');

// 同步全局变量（兼容旧版代码）
window.currentUser = null;
window.currentUserType = 'guest';
window.USER_TYPES = {
    GUEST: 'guest',
    PREMIUM: 'premium',
    PRIVILEGED: 'privileged',
    ADMIN: 'admin'
};

console.log('🔐 统一认证管理器已初始化');
