<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalStorage 调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #005a87; }
        .clear-btn { background: #dc3545; }
        .clear-btn:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 LocalStorage 调试工具</h1>
        
        <div class="section">
            <h2>📊 当前 LocalStorage 状态</h2>
            <button class="button" onclick="showAllStorage()">显示所有存储</button>
            <button class="button" onclick="showAuthStorage()">显示认证相关</button>
            <button class="button clear-btn" onclick="clearAllAuth()">清除所有认证</button>
            <div id="storage-display"></div>
        </div>
        
        <div class="section">
            <h2>🔐 认证状态检查</h2>
            <button class="button" onclick="checkAuthStatus()">检查认证状态</button>
            <div id="auth-status"></div>
        </div>
        
        <div class="section">
            <h2>🧪 测试登录</h2>
            <input type="email" id="test-email" placeholder="邮箱" value="<EMAIL>">
            <input type="password" id="test-password" placeholder="密码" value="admin123">
            <button class="button" onclick="testLogin()">测试登录</button>
            <div id="login-result"></div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="supabase-simple.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/auth-manager.js"></script>

    <script>
        function showAllStorage() {
            const display = document.getElementById('storage-display');
            const storage = {};
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                storage[key] = localStorage.getItem(key);
            }
            
            display.innerHTML = `
                <h3>所有 LocalStorage 数据:</h3>
                <pre>${JSON.stringify(storage, null, 2)}</pre>
            `;
        }
        
        function showAuthStorage() {
            const display = document.getElementById('storage-display');
            const authKeys = [
                'simple_auth_user',
                'simple_auth_login_time',
                'admin_logged_in',
                'admin_username',
                'admin_user_id',
                'admin_email',
                'admin_login_time'
            ];
            
            const authStorage = {};
            authKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value !== null) {
                    authStorage[key] = value;
                }
            });
            
            display.innerHTML = `
                <h3>认证相关 LocalStorage 数据:</h3>
                <pre>${JSON.stringify(authStorage, null, 2)}</pre>
            `;
        }
        
        function clearAllAuth() {
            const authKeys = [
                'simple_auth_user',
                'simple_auth_login_time',
                'admin_logged_in',
                'admin_username',
                'admin_user_id',
                'admin_email',
                'admin_login_time'
            ];
            
            authKeys.forEach(key => {
                localStorage.removeItem(key);
            });
            
            document.getElementById('storage-display').innerHTML = `
                <div class="status success">✅ 所有认证数据已清除</div>
            `;
        }
        
        function checkAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            let status = '<h3>认证状态检查:</h3>';
            
            // 检查简化认证
            const savedUser = localStorage.getItem('simple_auth_user');
            const loginTime = localStorage.getItem('simple_auth_login_time');
            
            if (savedUser && loginTime) {
                const user = JSON.parse(savedUser);
                const now = Date.now();
                const loginTimestamp = parseInt(loginTime);
                const expireTime = 24 * 60 * 60 * 1000; // 24小时
                const isExpired = now - loginTimestamp >= expireTime;
                
                status += `
                    <div class="status ${isExpired ? 'error' : 'success'}">
                        <strong>简化认证:</strong> ${isExpired ? '已过期' : '有效'}<br>
                        用户: ${user.username}<br>
                        邮箱: ${user.email}<br>
                        权限: ${user.user_type}<br>
                        登录时间: ${new Date(loginTimestamp).toLocaleString()}<br>
                        ${isExpired ? '过期时间: ' + new Date(loginTimestamp + expireTime).toLocaleString() : ''}
                    </div>
                `;
            } else {
                status += '<div class="status info">简化认证: 未登录</div>';
            }
            
            // 检查管理员认证
            const isAdminLoggedIn = localStorage.getItem('admin_logged_in') === 'true';
            if (isAdminLoggedIn) {
                status += `
                    <div class="status success">
                        <strong>管理员认证:</strong> 已登录<br>
                        用户名: ${localStorage.getItem('admin_username')}<br>
                        邮箱: ${localStorage.getItem('admin_email')}
                    </div>
                `;
            } else {
                status += '<div class="status info">管理员认证: 未登录</div>';
            }
            
            statusDiv.innerHTML = status;
        }
        
        async function testLogin() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const resultDiv = document.getElementById('login-result');
            
            if (!email || !password) {
                resultDiv.innerHTML = '<div class="status error">请输入邮箱和密码</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="status info">正在测试登录...</div>';
            
            try {
                // 使用全局 loginUser 函数
                if (typeof loginUser === 'function') {
                    const result = await loginUser(email, password);
                    
                    if (result.success) {
                        resultDiv.innerHTML = `
                            <div class="status success">
                                ✅ 登录成功!<br>
                                用户: ${result.user.username}<br>
                                权限: ${result.user.user_type}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="status error">
                                ❌ 登录失败: ${result.message}
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="status error">❌ loginUser 函数未找到</div>';
                }
            } catch (error) {
                console.error('登录测试错误:', error);
                resultDiv.innerHTML = `
                    <div class="status error">
                        ❌ 登录测试出错: ${error.message}
                    </div>
                `;
            }
        }
        
        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkAuthStatus();
                showAuthStorage();
            }, 1000);
        });
    </script>
</body>
</html>