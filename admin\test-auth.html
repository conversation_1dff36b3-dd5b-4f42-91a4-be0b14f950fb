<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>认证系统测试页面</h1>
        
        <div id="status"></div>
        
        <h2>测试操作</h2>
        <button onclick="testAuth()">测试认证状态</button>
        <button onclick="setTempAuth()">设置临时认证</button>
        <button onclick="clearAuth()">清除认证</button>
        <button onclick="goToCustomerService()">访问客服管理</button>
        
        <h2>认证信息</h2>
        <pre id="authInfo"></pre>
        
        <h2>控制台日志</h2>
        <pre id="console"></pre>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 管理后台专用Supabase配置
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );
        console.log('🔧 [ADMIN-TEST] Supabase客户端初始化完成');
    </script>
    <script src="js/admin-common.js"></script>

    <script>
        // 重定向控制台输出
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function updateConsole(message, type = 'log') {
            const consoleElement = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            consoleElement.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            updateConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            updateConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            updateConsole(args.join(' '), 'warn');
        };

        function showStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function updateAuthInfo() {
            const authInfo = {
                localStorage: {
                    admin_logged_in: localStorage.getItem('admin_logged_in'),
                    admin_username: localStorage.getItem('admin_username'),
                    admin_user_id: localStorage.getItem('admin_user_id')
                },
                supabase: 'checking...',
                authManager: window.authManager ? 'available' : 'not available'
            };
            
            document.getElementById('authInfo').textContent = JSON.stringify(authInfo, null, 2);
            
            // 异步检查Supabase会话
            if (window.supabase) {
                window.supabase.auth.getSession().then(({ data: { session } }) => {
                    authInfo.supabase = session ? 'logged in' : 'not logged in';
                    document.getElementById('authInfo').textContent = JSON.stringify(authInfo, null, 2);
                });
            }
        }

        async function testAuth() {
            try {
                showStatus('正在测试认证...', 'info');
                const result = await checkAdminAuth();
                showStatus(`认证结果: ${result ? '成功' : '失败'}`, result ? 'success' : 'error');
                updateAuthInfo();
            } catch (error) {
                showStatus(`认证测试失败: ${error.message}`, 'error');
                console.error('认证测试错误:', error);
            }
        }

        function setTempAuth() {
            localStorage.setItem('admin_logged_in', 'true');
            localStorage.setItem('admin_username', 'test_admin');
            localStorage.setItem('admin_user_id', 'test_123');
            showStatus('临时认证已设置', 'success');
            updateAuthInfo();
        }

        function clearAuth() {
            localStorage.removeItem('admin_logged_in');
            localStorage.removeItem('admin_username');
            localStorage.removeItem('admin_user_id');
            showStatus('认证已清除', 'info');
            updateAuthInfo();
        }

        function goToCustomerService() {
            window.location.href = 'customer-service.html';
        }

        // 页面加载时更新信息
        document.addEventListener('DOMContentLoaded', function() {
            updateAuthInfo();
            console.log('认证测试页面已加载');
        });
    </script>
</body>
</html>
