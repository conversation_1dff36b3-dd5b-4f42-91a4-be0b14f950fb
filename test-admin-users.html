<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理测试 - 春晟机械</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户管理功能测试</h1>
        
        <div class="test-section info">
            <h3>测试说明</h3>
            <p>这个页面用于测试用户管理功能是否正常工作，特别是解决406错误问题。</p>
            <p><strong>管理员登录状态:</strong> <span id="admin-status">检测中...</span></p>
        </div>

        <div class="test-section">
            <h3>测试操作</h3>
            <button onclick="testAdminAuth()">测试管理员认证</button>
            <button onclick="testLoadUsers()">测试加载用户列表</button>
            <button onclick="testDirectQuery()">测试直接查询</button>
            <button onclick="testRPCFunction()">测试RPC函数</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 初始化 Supabase 客户端
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `
                <h4>${title}</h4>
                <pre>${content}</pre>
                <small>时间: ${new Date().toLocaleString()}</small>
            `;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testAdminAuth() {
            const adminSession = localStorage.getItem('admin_session');
            const loginTime = localStorage.getItem('admin_login_time');
            
            let authStatus = '未登录';
            let authDetails = '';
            
            if (adminSession === 'true' && loginTime) {
                const loginDate = new Date(loginTime);
                const now = new Date();
                const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
                
                if (hoursDiff < 24) {
                    authStatus = '已登录';
                    authDetails = `登录时间: ${loginDate.toLocaleString()}\n会话剩余: ${(24 - hoursDiff).toFixed(1)} 小时`;
                } else {
                    authStatus = '会话已过期';
                    authDetails = `登录时间: ${loginDate.toLocaleString()}\n过期时间: ${hoursDiff.toFixed(1)} 小时前`;
                }
            }
            
            document.getElementById('admin-status').textContent = authStatus;
            addResult('管理员认证状态', `状态: ${authStatus}\n${authDetails}`, authStatus === '已登录' ? 'success' : 'error');
        }

        async function testLoadUsers() {
            try {
                addResult('用户列表测试', '正在加载用户列表...', 'info');
                
                // 尝试使用RPC函数
                let users, error;
                
                try {
                    const result = await supabase.rpc('get_all_users_admin');
                    users = result.data;
                    error = result.error;
                    addResult('RPC函数调用', `成功调用 get_all_users_admin\n返回数据: ${JSON.stringify(users, null, 2)}`, 'success');
                } catch (rpcError) {
                    addResult('RPC函数调用', `RPC函数调用失败: ${rpcError.message}`, 'error');
                    
                    // 如果RPC失败，尝试直接查询
                    const result = await supabase
                        .from('users')
                        .select('*')
                        .order('created_at', { ascending: false });
                    
                    users = result.data;
                    error = result.error;
                    addResult('直接查询', `直接查询用户表\n返回数据: ${JSON.stringify(users, null, 2)}`, users ? 'success' : 'error');
                }
                
                if (error) {
                    addResult('用户列表结果', `查询失败: ${error.message}\n错误代码: ${error.code}\n详情: ${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    const filteredUsers = (users || []).filter(user => user.user_type !== 'admin');
                    addResult('用户列表结果', `查询成功！\n总用户数: ${users ? users.length : 0}\n非管理员用户数: ${filteredUsers.length}\n用户列表: ${JSON.stringify(filteredUsers, null, 2)}`, 'success');
                    
                    // 显示用户表格
                    if (filteredUsers.length > 0) {
                        displayUsersTable(filteredUsers);
                    }
                }
            } catch (error) {
                addResult('用户列表结果', `异常: ${error.message}\n堆栈: ${error.stack}`, 'error');
            }
        }

        async function testDirectQuery() {
            try {
                addResult('直接查询测试', '正在执行直接查询...', 'info');
                
                const { data, error } = await supabase
                    .from('users')
                    .select('id, username, email, user_type, created_at')
                    .limit(10);
                
                if (error) {
                    addResult('直接查询结果', `查询失败: ${error.message}\n状态码: ${error.code}\n详情: ${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    addResult('直接查询结果', `查询成功！\n返回记录数: ${data.length}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                }
            } catch (error) {
                addResult('直接查询结果', `异常: ${error.message}`, 'error');
            }
        }

        async function testRPCFunction() {
            try {
                addResult('RPC函数测试', '正在测试RPC函数...', 'info');
                
                const { data, error } = await supabase.rpc('get_all_users_admin');
                
                if (error) {
                    addResult('RPC函数结果', `RPC调用失败: ${error.message}\n状态码: ${error.code}\n详情: ${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    addResult('RPC函数结果', `RPC调用成功！\n返回记录数: ${data ? data.length : 0}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                }
            } catch (error) {
                addResult('RPC函数结果', `异常: ${error.message}`, 'error');
            }
        }

        function displayUsersTable(users) {
            const tableHtml = `
                <div class="test-section">
                    <h4>用户列表</h4>
                    <table>
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>用户类型</th>
                                <th>公司</th>
                                <th>状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td>${user.username || '-'}</td>
                                    <td>${user.email || '-'}</td>
                                    <td>${user.user_type || '-'}</td>
                                    <td>${user.company_name || '-'}</td>
                                    <td>${user.is_active ? '激活' : '未激活'}</td>
                                    <td>${user.created_at ? new Date(user.created_at).toLocaleString() : '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            document.getElementById('results').insertAdjacentHTML('beforeend', tableHtml);
        }

        // 页面加载时自动测试认证状态
        window.addEventListener('load', function() {
            setTimeout(testAdminAuth, 500);
        });
    </script>
</body>
</html>
