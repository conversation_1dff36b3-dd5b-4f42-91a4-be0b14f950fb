<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复用户密码 - 管理工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.success {
            background-color: #28a745;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 用户密码修复工具</h1>
        
        <div class="warning">
            <h3>⚠️ 问题说明</h3>
            <p>从Supabase控制台可以看到，用户表中的 <code>password_hash</code> 字段都是 NULL。</p>
            <p>这导致用户无法正常登录，需要为用户设置密码。</p>
        </div>

        <div>
            <h3>📋 操作选项</h3>
            <button onclick="loadUsers()">加载用户列表</button>
            <button onclick="createAdminUser()" class="success">创建管理员用户</button>
            <button onclick="resetAllPasswords()" class="danger">重置所有用户密码</button>
        </div>

        <div id="status"></div>
        <div id="users-container"></div>
        
        <div id="admin-form" style="display: none;">
            <h3>创建管理员账户</h3>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="admin-username" value="admin">
            </div>
            <div class="form-group">
                <label>邮箱:</label>
                <input type="email" id="admin-email" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="admin-password" value="admin123">
            </div>
            <button onclick="saveAdminUser()" class="success">保存管理员</button>
            <button onclick="hideAdminForm()">取消</button>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 初始化 Supabase 客户端
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        function showMessage(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        async function loadUsers() {
            try {
                showMessage('正在加载用户数据...', 'info');
                
                const { data: users, error } = await supabase
                    .from('users')
                    .select('*')
                    .order('created_at', { ascending: false });
                
                if (error) {
                    showMessage(`❌ 加载失败: ${error.message}`, 'error');
                    return;
                }
                
                displayUsers(users);
                showMessage(`✅ 成功加载 ${users.length} 个用户`, 'success');
                
            } catch (error) {
                showMessage(`❌ 加载异常: ${error.message}`, 'error');
            }
        }

        function displayUsers(users) {
            const container = document.getElementById('users-container');
            
            const html = `
                <h3>用户列表 (${users.length} 个用户)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>用户类型</th>
                            <th>密码状态</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.username || '-'}</td>
                                <td>${user.email || '-'}</td>
                                <td>${user.user_type || 'guest'}</td>
                                <td>${user.password_hash ? '✅ 已设置' : '❌ 未设置'}</td>
                                <td>${user.is_active ? '活跃' : '禁用'}</td>
                                <td>
                                    <button onclick="resetUserPassword('${user.id}', '${user.username}')">重置密码</button>
                                    ${user.user_type !== 'admin' ? `<button onclick="makeAdmin('${user.id}')">设为管理员</button>` : ''}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }

        async function resetUserPassword(userId, username) {
            const newPassword = prompt(`为用户 ${username} 设置新密码:`, 'password123');
            if (!newPassword) return;
            
            try {
                showMessage(`正在为用户 ${username} 重置密码...`, 'info');
                
                // 生成简单的密码哈希（实际项目中应该使用更安全的方法）
                const passwordHash = btoa(newPassword); // 简单的base64编码，仅用于演示
                
                const { error } = await supabase
                    .from('users')
                    .update({ password_hash: passwordHash })
                    .eq('id', userId);
                
                if (error) {
                    showMessage(`❌ 重置失败: ${error.message}`, 'error');
                    return;
                }
                
                showMessage(`✅ 用户 ${username} 密码重置成功！新密码: ${newPassword}`, 'success');
                loadUsers(); // 重新加载用户列表
                
            } catch (error) {
                showMessage(`❌ 重置异常: ${error.message}`, 'error');
            }
        }

        async function makeAdmin(userId) {
            if (!confirm('确定要将此用户设为管理员吗？')) return;
            
            try {
                const { error } = await supabase
                    .from('users')
                    .update({ user_type: 'admin' })
                    .eq('id', userId);
                
                if (error) {
                    showMessage(`❌ 设置失败: ${error.message}`, 'error');
                    return;
                }
                
                showMessage('✅ 用户已设为管理员', 'success');
                loadUsers();
                
            } catch (error) {
                showMessage(`❌ 设置异常: ${error.message}`, 'error');
            }
        }

        function createAdminUser() {
            document.getElementById('admin-form').style.display = 'block';
        }

        function hideAdminForm() {
            document.getElementById('admin-form').style.display = 'none';
        }

        async function saveAdminUser() {
            const username = document.getElementById('admin-username').value;
            const email = document.getElementById('admin-email').value;
            const password = document.getElementById('admin-password').value;
            
            if (!username || !email || !password) {
                alert('请填写所有字段');
                return;
            }
            
            try {
                showMessage('正在创建管理员账户...', 'info');
                
                // 检查用户是否已存在
                const { data: existingUsers } = await supabase
                    .from('users')
                    .select('*')
                    .or(`username.eq.${username},email.eq.${email}`);
                
                if (existingUsers && existingUsers.length > 0) {
                    showMessage('❌ 用户名或邮箱已存在', 'error');
                    return;
                }
                
                // 创建用户记录
                const passwordHash = btoa(password); // 简单编码
                
                const { error } = await supabase
                    .from('users')
                    .insert([{
                        username: username,
                        email: email,
                        password_hash: passwordHash,
                        user_type: 'admin',
                        is_active: true
                    }]);
                
                if (error) {
                    showMessage(`❌ 创建失败: ${error.message}`, 'error');
                    return;
                }
                
                showMessage(`✅ 管理员账户创建成功！<br>用户名: ${username}<br>密码: ${password}`, 'success');
                hideAdminForm();
                loadUsers();
                
            } catch (error) {
                showMessage(`❌ 创建异常: ${error.message}`, 'error');
            }
        }

        async function resetAllPasswords() {
            if (!confirm('确定要重置所有用户的密码吗？这将为所有用户设置默认密码 "password123"')) return;
            
            try {
                showMessage('正在重置所有用户密码...', 'info');
                
                const { data: users } = await supabase
                    .from('users')
                    .select('id, username');
                
                const defaultPassword = 'password123';
                const passwordHash = btoa(defaultPassword);
                
                for (const user of users) {
                    await supabase
                        .from('users')
                        .update({ password_hash: passwordHash })
                        .eq('id', user.id);
                }
                
                showMessage(`✅ 所有用户密码已重置为: ${defaultPassword}`, 'success');
                loadUsers();
                
            } catch (error) {
                showMessage(`❌ 批量重置失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动加载用户
        window.addEventListener('load', function() {
            setTimeout(loadUsers, 1000);
        });
    </script>
</body>
</html>