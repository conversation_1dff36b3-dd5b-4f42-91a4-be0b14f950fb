<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码重置邮件发送工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .note {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📧 密码重置邮件发送工具</h1>
        
        <div class="note">
            <strong>说明：</strong>Supabase 出于安全考虑，只允许通过邮件重置密码。此工具将向指定邮箱发送密码重置链接。
        </div>
        
        <div class="form-group">
            <label for="email">用户邮箱:</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="输入要重置密码的用户邮箱">
        </div>
        
        <button id="sendBtn" onclick="sendResetEmail()">发送密码重置邮件</button>
        
        <div id="message" class="message"></div>
    </div>

    <!-- 引入 Supabase -->
    <script src="supabase-js.min.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        async function sendResetEmail() {
            const email = document.getElementById('email').value.trim();
            const sendBtn = document.getElementById('sendBtn');

            if (!email) {
                showMessage('请输入邮箱地址', 'error');
                return;
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showMessage('请输入有效的邮箱地址', 'error');
                return;
            }

            try {
                sendBtn.disabled = true;
                sendBtn.textContent = '发送中...';
                showMessage('正在发送密码重置邮件...', 'info');

                // 使用 Supabase 客户端发送密码重置邮件
                const { error } = await supabase.auth.resetPasswordForEmail(email, {
                    redirectTo: `${window.location.origin}/reset-password-complete.html`
                });

                if (error) {
                    throw error;
                }

                showMessage(`✅ 密码重置邮件已成功发送到 <strong>${email}</strong><br><br>
                    请按照以下步骤操作：<br>
                    1. 检查邮箱（包括垃圾邮件文件夹）<br>
                    2. 点击邮件中的重置链接<br>
                    3. 在打开的页面中设置新密码<br>
                    4. 使用新密码登录系统`, 'success');

            } catch (error) {
                console.error('发送重置邮件失败:', error);
                let errorMessage = '发送密码重置邮件失败';
                
                if (error.message.includes('User not found')) {
                    errorMessage = '该邮箱地址未注册';
                } else if (error.message.includes('Email rate limit exceeded')) {
                    errorMessage = '发送频率过快，请稍后再试';
                } else {
                    errorMessage += ': ' + error.message;
                }
                
                showMessage(errorMessage, 'error');
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送密码重置邮件';
            }
        }

        // 页面加载时检查 Supabase 连接
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof supabase === 'undefined') {
                    showMessage('❌ Supabase 未正确加载，请刷新页面重试', 'error');
                } else {
                    showMessage('✅ 系统已就绪，可以发送密码重置邮件', 'info');
                }
            }, 1000);
        });

        // 回车键发送
        document.getElementById('email').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendResetEmail();
            }
        });
    </script>
</body>
</html>
