<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术文档查看器 - 春盛机械</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: white;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
        }

        .main-container {
            flex: 1;
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
            width: 100%;
        }

        .pdf-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .pdf-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pdf-title {
            font-size: 20px;
            font-weight: bold;
        }

        .pdf-actions {
            display: flex;
            gap: 15px;
        }

        .btn-download {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-download:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .pdf-viewer {
            width: 100%;
            height: 80vh;
            border: none;
            background: #f8f9fa;
        }

        .product-info {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .info-label {
            font-weight: 600;
            color: #333;
            min-width: 80px;
        }

        .info-value {
            color: #666;
            flex: 1;
        }

        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 400px;
            color: #666;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            color: #c53030;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .success {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #2f855a;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 15px;
            }

            .nav-buttons {
                flex-wrap: wrap;
                justify-content: center;
            }

            .pdf-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .pdf-actions {
                justify-content: center;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .pdf-viewer {
                height: 60vh;
            }
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <img src="logo.png" alt="春盛机械" onerror="this.style.display='none'">
                <div class="logo-text">🏭 春盛机械</div>
            </div>
            <div class="nav-buttons">
                <a href="index.html" class="btn btn-secondary">🏠 首页</a>
                <a href="products.html" class="btn btn-secondary">📦 产品中心</a>
                <a href="javascript:history.back()" class="btn btn-primary">← 返回</a>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-container">
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在加载技术文档...</p>
        </div>

        <div id="content" style="display: none;">
            <!-- 产品信息 -->
            <div class="product-info">
                <h2 style="margin-bottom: 20px; color: #333;">📋 产品信息</h2>
                <div id="product-details" class="info-grid">
                    <!-- 产品信息将通过JavaScript填充 -->
                </div>
            </div>

            <!-- PDF查看器 -->
            <div class="pdf-container">
                <div class="pdf-header">
                    <div class="pdf-title">📄 技术文档</div>
                    <div class="pdf-actions">
                        <button id="fullscreen-btn" class="btn btn-download">🔍 全屏查看</button>
                        <button id="download-btn" class="btn btn-download">📥 下载文档</button>
                    </div>
                </div>
                <iframe id="pdf-viewer" class="pdf-viewer" title="PDF文档查看器"></iframe>
            </div>
        </div>

        <div id="error" class="error" style="display: none;">
            <h3>❌ 加载失败</h3>
            <p id="error-message"></p>
            <button onclick="location.reload()" class="btn btn-primary" style="margin-top: 15px;">🔄 重新加载</button>
        </div>
    </main>

    <script src="js/supabase-config.js"></script>
    <script src="js/auth-manager.js"></script>
    <script>
        // 从URL参数获取产品ID
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 等待认证管理器初始化
            await new Promise(resolve => setTimeout(resolve, 500));

            // 检查用户权限
            if (!canDownload()) {
                showError('您的权限不足，无法查看PDF文档。特许用户可查看和下载PDF技术文档。');
                return;
            }

            if (!productId) {
                showError('缺少产品ID参数');
            } else {
                loadProductAndPDF(productId);
            }
        });

        async function loadProductAndPDF(productId) {
            try {
                // 获取产品信息
                const { data: product, error } = await supabase
                    .from('products')
                    .select('*')
                    .eq('id', productId)
                    .single();

                if (error) throw error;
                if (!product) throw new Error('产品不存在');

                // 检查PDF
                if (!product.attachment_path || !product.attachment_path.includes('supabase.co')) {
                    throw new Error('该产品暂无技术文档');
                }

                // 显示产品信息
                displayProductInfo(product);

                // 显示PDF
                displayPDF(product);

                // 隐藏加载动画，显示内容
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';

            } catch (error) {
                console.error('加载失败:', error);
                showError(error.message);
            }
        }

        function displayProductInfo(product) {
            const detailsContainer = document.getElementById('product-details');
            detailsContainer.innerHTML = `
                <div class="info-item">
                    <div class="info-label">🏷️ 产品名称:</div>
                    <div class="info-value">${product.product_name}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">🔢 数据ID:</div>
                    <div class="info-value">${product.data_id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">📂 产品类别:</div>
                    <div class="info-value">${product.product_category}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">📏 产品规格:</div>
                    <div class="info-value">${product.specifications}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">🔧 材质:</div>
                    <div class="info-value">${product.material}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">📦 存货编码:</div>
                    <div class="info-value">${product.stock_code}</div>
                </div>
            `;
        }

        function displayPDF(product) {
            const pdfViewer = document.getElementById('pdf-viewer');
            const fileName = `${product.data_id}_技术文档.pdf`;
            
            // 设置PDF查看器
            pdfViewer.src = product.attachment_path;
            
            // 设置下载按钮
            document.getElementById('download-btn').onclick = function() {
                downloadPDF(product.attachment_path, fileName);
            };
            
            // 设置全屏按钮
            document.getElementById('fullscreen-btn').onclick = function() {
                window.open(product.attachment_path, '_blank');
            };
        }

        function downloadPDF(pdfUrl, fileName) {
            try {
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = fileName;
                link.target = '_blank';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showSuccess(`文档 "${fileName}" 下载已开始`);
            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败: ' + error.message);
            }
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            document.querySelector('.main-container').insertBefore(successDiv, document.querySelector('.main-container').firstChild);
            
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }
    </script>
</body>
</html>
