// 产品页面专用JavaScript

let allProducts = []; // 存储所有产品数据
let filteredProducts = []; // 存储筛选后的产品数据

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeProductsPage();
});

// 初始化产品页面
async function initializeProductsPage() {
    console.log('开始初始化产品页面...');

    // 加载所有产品
    await loadAllProductsData();

    // 初始化筛选功能
    initializeFilters();

    // 显示产品
    displayFilteredProducts();

    // 初始化智能搜索功能
    await initializeSmartProductSearch();
}

// 初始化智能产品搜索
async function initializeSmartProductSearch() {
    console.log('初始化智能搜索功能...');

    try {
        // 确保智能搜索对象存在
        if (typeof SmartSearch !== 'undefined') {
            window.smartSearch = new SmartSearch();
            smartSearch.setProducts(allProducts);
            console.log('智能搜索对象已创建，产品数据已设置');
        } else {
            console.warn('SmartSearch 类未找到，跳过智能搜索初始化');
        }

        // 初始化搜索界面
        if (typeof initializeSmartSearch === 'function') {
            initializeSmartSearch();
        }

        // 绑定智能搜索事件
        const smartSearchBtn = document.getElementById('smart-search-btn');
        const smartSearchInput = document.getElementById('smart-search-input');

        if (smartSearchBtn) {
            smartSearchBtn.addEventListener('click', performSmartSearch);
        }

        if (smartSearchInput) {
            smartSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSmartSearch();
                }
            });

            // 实时搜索建议
            smartSearchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length >= 2) {
                    if (typeof showSearchHistory === 'function') {
                        showSearchHistory();
                    }
                }
            });
        }

        // 绑定筛选器事件
        const categoryFilter = document.getElementById('category-filter');
        const materialFilter = document.getElementById('material-filter');
        const resetBtn = document.getElementById('reset-filters-btn');

        if (categoryFilter) categoryFilter.addEventListener('change', performSmartSearch);
        if (materialFilter) materialFilter.addEventListener('change', performSmartSearch);
        if (resetBtn) resetBtn.addEventListener('click', resetAllFilters);

        console.log('智能搜索功能初始化完成');
    } catch (error) {
        console.error('智能搜索初始化失败:', error);
    }
}

// 执行智能搜索（如果智能搜索模块不可用，则使用基本搜索）
function performSmartSearch() {
    try {
        if (typeof smartSearch !== 'undefined' && smartSearch.search) {
            // 使用智能搜索
            const query = document.getElementById('smart-search-input')?.value.trim() || '';
            const includeApproximate = document.getElementById('include-approximate')?.checked || false;
            const tolerance = parseInt(document.getElementById('tolerance-select')?.value || '3');
            const category = document.getElementById('category-filter')?.value || '';
            const material = document.getElementById('material-filter')?.value || '';

            console.log('执行智能搜索:', query);

            const results = smartSearch.search(query, {
                tolerance: tolerance,
                includeApproximate: includeApproximate
            });

            // 合并精确和近似结果
            let combinedResults = [...results.exact];
            if (includeApproximate) {
                combinedResults = combinedResults.concat(results.approximate);
            }

            // 应用传统筛选
            if (category || material) {
                combinedResults = combinedResults.filter(product => {
                    const categoryMatch = !category || product.product_category === category;
                    const materialMatch = !material || product.material === material;
                    return categoryMatch && materialMatch;
                });
            }

            // 显示智能搜索结果
            const container = document.getElementById('all-products-grid');
            if (typeof displaySmartSearchResults === 'function') {
                displaySmartSearchResults(results, container);
            } else {
                // 回退到传统显示方式
                filteredProducts = combinedResults;
                displayFilteredProducts();
            }

            // 更新统计信息
            if (typeof updateSearchStats === 'function') {
                updateSearchStats(results, query);
            }

            // 显示搜索历史
            if (typeof showSearchHistory === 'function') {
                showSearchHistory();
            }

            console.log('智能搜索结果:', combinedResults.length, '个产品');
        } else {
            console.log('智能搜索不可用，使用基本搜索');
            applyFilters();
            displayFilteredProducts();
        }
    } catch (error) {
        console.error('智能搜索出错:', error);
        console.log('回退到基本搜索');
        applyFilters();
        displayFilteredProducts();
    }
}

// 重置所有筛选器
function resetAllFilters() {
    const smartSearchInput = document.getElementById('smart-search-input');
    const categoryFilter = document.getElementById('category-filter');
    const materialFilter = document.getElementById('material-filter');

    if (smartSearchInput) smartSearchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (materialFilter) materialFilter.value = '';

    filteredProducts = [...allProducts];
    displayFilteredProducts();
}

// 加载所有产品数据
async function loadAllProductsData() {
    const container = document.getElementById('all-products-grid');
    if (!container) {
        console.error('产品容器未找到');
        return;
    }

    // 显示加载状态
    container.innerHTML = '<div style="text-align: center; padding: 50px;"><div style="font-size: 18px; color: #666;">正在加载产品数据...</div></div>';

    try {
        console.log('开始加载产品数据...');

        // 获取产品数据，包含车型信息
        const { data: products, error } = await supabase
            .from('products')
            .select(`
                *,
                car_model:car_models(
                    id,
                    brand,
                    model,
                    year_range
                )
            `)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('加载产品失败:', error);
            // 回退到基本产品加载
            if (typeof getProducts === 'function') {
                allProducts = await getProducts();
            } else {
                allProducts = [];
            }
        } else {
            allProducts = products || [];
        }

        console.log('加载到的产品数据:', allProducts);
        console.log('产品排序顺序检查:');
        if (allProducts && allProducts.length > 0) {
            allProducts.forEach((product, index) => {
                console.log(`${index + 1}. ${product.product_name} - 创建时间: ${product.created_at} - 车型: ${product.car_model ? product.car_model.brand + ' ' + product.car_model.model : '无'}`);
            });
        }

        if (!allProducts || allProducts.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #666;">暂无产品数据</p>';
            return;
        }

        filteredProducts = [...allProducts]; // 初始化筛选结果
        console.log('产品数据加载成功，共', allProducts.length, '个产品');
    } catch (error) {
        console.error('加载产品失败:', error);
        container.innerHTML = '<p style="text-align: center; color: #666;">加载产品失败，请稍后重试</p>';
    }
}

// 初始化筛选功能
function initializeFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('search-input');
    const searchBtn = document.getElementById('search-btn');
    const resetBtn = document.getElementById('reset-btn');

    // 检查是否有来自首页的分类选择
    const selectedCategory = sessionStorage.getItem('selectedCategory');
    if (selectedCategory && categoryFilter) {
        // 设置分类筛选器的值
        categoryFilter.value = selectedCategory;
        console.log('从首页接收到分类筛选:', selectedCategory);

        // 清除sessionStorage中的分类选择
        sessionStorage.removeItem('selectedCategory');

        // 应用筛选
        setTimeout(() => {
            applyFilters();
        }, 100);
    }

    // 类别筛选事件
    if (categoryFilter) {
        categoryFilter.addEventListener('change', applyFilters);
    }

    // 搜索按钮事件
    if (searchBtn) {
        searchBtn.addEventListener('click', applyFilters);
    }

    // 搜索输入框回车事件
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }

    // 重置按钮事件
    if (resetBtn) {
        resetBtn.addEventListener('click', resetFilters);
    }
}

// 应用筛选
function applyFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('search-input');
    
    const selectedCategory = categoryFilter ? categoryFilter.value : '';
    const searchTerm = searchInput ? searchInput.value.trim().toLowerCase() : '';
    
    // 筛选产品
    filteredProducts = allProducts.filter(product => {
        // 类别筛选
        const categoryMatch = !selectedCategory || product.product_category === selectedCategory;
        
        // 搜索筛选
        const searchMatch = !searchTerm || 
            product.product_name.toLowerCase().includes(searchTerm) ||
            product.stock_code.toLowerCase().includes(searchTerm) ||
            product.data_id.toLowerCase().includes(searchTerm);
        
        return categoryMatch && searchMatch;
    });
    
    // 显示筛选结果
    displayFilteredProducts();
}

// 重置筛选
function resetFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const searchInput = document.getElementById('search-input');
    
    if (categoryFilter) categoryFilter.value = '';
    if (searchInput) searchInput.value = '';
    
    filteredProducts = [...allProducts];
    displayFilteredProducts();
}

// 显示筛选后的产品
function displayFilteredProducts() {
    const container = document.getElementById('all-products-grid');
    if (!container) {
        console.error('产品容器未找到');
        return;
    }

    console.log('开始显示产品，筛选后的产品数量:', filteredProducts.length);

    if (filteredProducts.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666; grid-column: 1 / -1;">没有找到符合条件的产品</p>';
        return;
    }

    // 生成产品HTML
    console.log('生成产品卡片...');
    const productsHTML = filteredProducts.map(product => {
        console.log('处理产品:', product.product_name);
        return createProductCard(product);
    }).join('');

    console.log('设置产品HTML到容器');
    container.innerHTML = productsHTML;
    
    // 不再需要复杂的事件处理，因为使用链接跳转
    
    // 更新结果统计
    updateResultsCount();
}

// 更新结果统计
function updateResultsCount() {
    // 可以在页面上显示筛选结果数量
    const totalCount = allProducts.length;
    const filteredCount = filteredProducts.length;
    
    // 如果有结果统计容器，更新它
    const statsContainer = document.getElementById('results-stats');
    if (statsContainer) {
        statsContainer.textContent = `显示 ${filteredCount} / ${totalCount} 个产品`;
    }
}

// 产品排序功能
function sortProducts(sortBy) {
    switch (sortBy) {
        case 'name':
            filteredProducts.sort((a, b) => a.product_name.localeCompare(b.product_name));
            break;
        case 'category':
            filteredProducts.sort((a, b) => a.product_category.localeCompare(b.product_category));
            break;
        case 'date':
            filteredProducts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            break;
        default:
            break;
    }
    
    displayFilteredProducts();
}

// 导出产品数据（管理员功能）
function exportProducts() {
    if (!currentUser || currentUserType !== 'admin') {
        alert('只有管理员可以导出产品数据');
        return;
    }
    
    // 创建CSV数据
    const headers = [
        '数据ID', '存货编码', '产品名称', '产品类别', '产品规格', '材质', '料厚',
        '备注', '外形编码', '主要工艺1', '主要工艺2', '主要工艺3', '主要工艺4',
        '工序数', '可变加工艺1', '可变加工艺2', '可变加工艺3'
    ];
    
    const csvContent = [
        headers.join(','),
        ...filteredProducts.map(product => [
            product.data_id,
            product.stock_code,
            product.product_name,
            product.product_category,
            product.specifications,
            product.material,
            product.thickness,
            product.remarks || '',
            product.shape_code || '',
            product.main_process_1 || '',
            product.main_process_2 || '',
            product.main_process_3 || '',
            product.main_process_4 || '',
            product.process_count || '',
            product.variable_process_1 || '',
            product.variable_process_2 || '',
            product.variable_process_3 || ''
        ].map(field => `"${field}"`).join(','))
    ].join('\n');
    
    // 下载CSV文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `产品数据_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 批量操作功能（管理员）
function initializeBatchOperations() {
    if (!currentUser || currentUserType !== 'admin') return;
    
    // 添加批量操作按钮
    const container = document.querySelector('.product-filters .filter-controls');
    if (container) {
        const batchHTML = `
            <div class="batch-operations" style="margin-left: auto; display: flex; gap: 10px;">
                <button id="select-all-btn" class="btn btn-secondary">全选</button>
                <button id="export-btn" class="btn">导出数据</button>
                <button id="batch-delete-btn" class="btn" style="background: #dc3545;">批量删除</button>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', batchHTML);
        
        // 添加事件监听
        document.getElementById('select-all-btn').addEventListener('click', toggleSelectAll);
        document.getElementById('export-btn').addEventListener('click', exportProducts);
        document.getElementById('batch-delete-btn').addEventListener('click', batchDeleteProducts);
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.product-checkbox');
    const selectAllBtn = document.getElementById('select-all-btn');
    
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    
    checkboxes.forEach(cb => {
        cb.checked = !allChecked;
    });
    
    selectAllBtn.textContent = allChecked ? '全选' : '取消全选';
}

// 批量删除产品
async function batchDeleteProducts() {
    const selectedIds = Array.from(document.querySelectorAll('.product-checkbox:checked'))
        .map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('请选择要删除的产品');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个产品吗？此操作不可恢复。`)) {
        return;
    }
    
    try {
        const { error } = await supabase
            .from('products')
            .delete()
            .in('id', selectedIds);
        
        if (error) throw error;
        
        alert('删除成功');
        await loadAllProductsData();
        displayFilteredProducts();
    } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败，请稍后重试');
    }
}
