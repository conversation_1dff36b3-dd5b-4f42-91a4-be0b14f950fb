#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件重命名工具
用于批量重命名图片和PDF文件，使其符合产品数据ID格式
"""

import os
import shutil
from pathlib import Path

def rename_files():
    """批量重命名文件"""
    
    # 配置区域 - 请根据需要修改
    source_dir = input("请输入源文件目录路径（直接回车使用当前目录）: ").strip()
    if not source_dir:
        source_dir = "."
    
    # 数据ID列表 - 根据CSV模板中的产品
    data_ids = [
        "A0014", "A0015", "A0016", "A0017", "A0018",  # 简化模板
        "A0004", "A0005", "A0006", "A0007", "A0008",  # 完整模板
        "A0009", "A0010", "A0011", "A0012", "A0013"
    ]
    
    # 支持的文件格式
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    pdf_extensions = ['.pdf']
    
    source_path = Path(source_dir)
    if not source_path.exists():
        print(f"错误：目录 {source_dir} 不存在")
        return
    
    # 获取所有文件
    all_files = [f for f in source_path.iterdir() if f.is_file()]
    image_files = [f for f in all_files if f.suffix.lower() in image_extensions]
    pdf_files = [f for f in all_files if f.suffix.lower() in pdf_extensions]
    
    print(f"\n找到 {len(image_files)} 个图片文件")
    print(f"找到 {len(pdf_files)} 个PDF文件")
    print(f"可用数据ID: {', '.join(data_ids)}")
    
    # 重命名图片文件
    if image_files:
        print("\n=== 重命名图片文件 ===")
        for i, file_path in enumerate(image_files):
            if i < len(data_ids):
                data_id = data_ids[i]
                new_name = f"{data_id}{file_path.suffix.lower()}"
                new_path = source_path / new_name
                
                try:
                    shutil.copy2(file_path, new_path)
                    print(f"✅ {file_path.name} -> {new_name}")
                except Exception as e:
                    print(f"❌ 重命名失败 {file_path.name}: {e}")
            else:
                print(f"⚠️  跳过 {file_path.name} (数据ID不足)")
    
    # 重命名PDF文件
    if pdf_files:
        print("\n=== 重命名PDF文件 ===")
        pdf_suffixes = ["技术文档", "说明书", "产品手册", "工艺文档", "检验标准"]
        
        for i, file_path in enumerate(pdf_files):
            if i < len(data_ids):
                data_id = data_ids[i]
                suffix = pdf_suffixes[i % len(pdf_suffixes)]
                new_name = f"{data_id}_{suffix}.pdf"
                new_path = source_path / new_name
                
                try:
                    shutil.copy2(file_path, new_path)
                    print(f"✅ {file_path.name} -> {new_name}")
                except Exception as e:
                    print(f"❌ 重命名失败 {file_path.name}: {e}")
            else:
                print(f"⚠️  跳过 {file_path.name} (数据ID不足)")
    
    print("\n=== 重命名完成 ===")
    print("重命名后的文件已保存在同一目录中")
    print("原文件保持不变")

def batch_rename_interactive():
    """交互式批量重命名"""
    print("=== 产品文件重命名工具 ===")
    print("此工具将帮助您批量重命名图片和PDF文件")
    print("重命名格式：")
    print("  图片: A0014.jpg, A0015.png 等")
    print("  PDF:  A0014_技术文档.pdf, A0015_说明书.pdf 等")
    print()
    
    while True:
        choice = input("请选择操作：\n1. 批量重命名文件\n2. 查看数据ID列表\n3. 退出\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            rename_files()
        elif choice == "2":
            print("\n可用的数据ID列表：")
            data_ids = [
                "A0014", "A0015", "A0016", "A0017", "A0018",
                "A0004", "A0005", "A0006", "A0007", "A0008",
                "A0009", "A0010", "A0011", "A0012", "A0013"
            ]
            for i, data_id in enumerate(data_ids, 1):
                print(f"  {i:2d}. {data_id}")
            print()
        elif choice == "3":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入\n")

if __name__ == "__main__":
    batch_rename_interactive()
