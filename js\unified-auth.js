// 简化统一认证系统 - 只使用Supabase认证
// 版本: 2.1
// 创建时间: 2025-07-16

class UnifiedAuth {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.userType = 'guest';
        this.isInitialized = false;
        this.initPromise = null;
        
        // 用户权限类型
        this.USER_TYPES = {
            GUEST: 'guest',
            PREMIUM: 'premium', 
            PRIVILEGED: 'privileged',
            ADMIN: 'admin'
        };

        // 存储键名 - 统一使用一套
        this.STORAGE_KEYS = {
            USER: 'auth_user',
            LOGIN_TIME: 'auth_login_time'
        };

        // 登录过期时间（24小时）
        this.EXPIRE_TIME = 24 * 60 * 60 * 1000;
        
        // 开始初始化
        this.initPromise = this.init();
    }

    async init() {
        if (this.isInitialized) {
            return;
        }

        console.log('🔐 [UNIFIED-AUTH] 开始初始化统一认证系统');
        
        try {
            // 1. 清理旧的认证数据
            this.cleanupOldAuth();
            
            // 2. 初始化Supabase客户端
            await this.initSupabase();
            
            // 3. 检查现有登录状态
            await this.checkExistingAuth();
            
            // 4. 设置全局引用
            this.setupGlobalReferences();
            
            this.isInitialized = true;
            console.log('🔐 [UNIFIED-AUTH] 统一认证系统初始化完成');
            console.log('🔐 [UNIFIED-AUTH] 当前状态:', {
                user: this.currentUser?.username || 'null',
                type: this.userType,
                initialized: this.isInitialized
            });
            
        } catch (error) {
            console.error('🔐 [UNIFIED-AUTH] 初始化失败:', error);
            this.isInitialized = false;
        }
    }

    // 清理旧的认证数据，避免冲突
    cleanupOldAuth() {
        console.log('🔐 [UNIFIED-AUTH] 清理旧的认证数据');
        
        const oldKeys = [
            'simple_auth_user',
            'simple_auth_login_time',
            'admin_logged_in',
            'admin_username', 
            'admin_user_id',
            'admin_email',
            'admin_login_time',
            'admin_session'
        ];

        let cleanedCount = 0;
        oldKeys.forEach(key => {
            if (localStorage.getItem(key) !== null) {
                localStorage.removeItem(key);
                cleanedCount++;
            }
        });

        if (cleanedCount > 0) {
            console.log(`🔐 [UNIFIED-AUTH] 清理了 ${cleanedCount} 个旧的认证数据`);
        }
    }

    // 初始化Supabase客户端
    async initSupabase() {
        console.log('🔐 [UNIFIED-AUTH] 初始化Supabase客户端');
        
        let retryCount = 0;
        const maxRetries = 30;
        
        while (retryCount < maxRetries) {
            // 检查简化版Supabase客户端
            if (typeof window.supabaseClient !== 'undefined' && window.supabaseClient) {
                this.supabase = window.supabaseClient;
                console.log('🔐 [UNIFIED-AUTH] Supabase客户端连接成功');
                return;
            }
            
            // 检查全局supabase对象
            if (typeof window.supabase !== 'undefined' && window.supabase && typeof window.supabase.createClient === 'function') {
                // 使用全局配置创建客户端
                if (window.SUPABASE_URL && window.SUPABASE_ANON_KEY) {
                    this.supabase = window.supabase.createClient(window.SUPABASE_URL, window.SUPABASE_ANON_KEY);
                    console.log('🔐 [UNIFIED-AUTH] 使用全局配置创建Supabase客户端');
                    return;
                }
            }
            
            console.log(`🔐 [UNIFIED-AUTH] 等待Supabase初始化... (${retryCount + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, 100));
            retryCount++;
        }
        
        console.warn('🔐 [UNIFIED-AUTH] Supabase客户端初始化超时，将使用离线模式');
        this.supabase = null;
    }

    // 检查现有认证状态
    async checkExistingAuth() {
        console.log('🔐 [UNIFIED-AUTH] 检查现有认证状态');
        
        const savedUser = localStorage.getItem(this.STORAGE_KEYS.USER);
        const loginTime = localStorage.getItem(this.STORAGE_KEYS.LOGIN_TIME);
        
        if (savedUser && loginTime) {
            try {
                const user = JSON.parse(savedUser);
                const loginTimestamp = parseInt(loginTime);
                const now = Date.now();
                
                // 检查是否过期
                if (now - loginTimestamp < this.EXPIRE_TIME) {
                    this.currentUser = user;
                    this.userType = user.user_type || this.USER_TYPES.PREMIUM;
                    
                    console.log('🔐 [UNIFIED-AUTH] 恢复用户登录状态:', user.username);
                    console.log('🔐 [UNIFIED-AUTH] 用户权限:', this.userType);
                    return;
                } else {
                    console.log('🔐 [UNIFIED-AUTH] 登录已过期，清除数据');
                    this.clearAuthData();
                }
            } catch (error) {
                console.error('🔐 [UNIFIED-AUTH] 解析用户数据失败:', error);
                this.clearAuthData();
            }
        }
        
        console.log('🔐 [UNIFIED-AUTH] 没有有效的登录状态');
    }

    // 设置全局引用（兼容旧代码）
    setupGlobalReferences() {
        // 设置全局变量
        window.currentUser = this.currentUser;
        window.currentUserType = this.userType;
        window.unifiedAuth = this;
        
        // 设置全局函数（兼容性）
        window.loginUser = this.login.bind(this);
        window.logoutUser = this.logout.bind(this);
        window.registerUser = this.register.bind(this);
        
        console.log('🔐 [UNIFIED-AUTH] 全局引用已设置');
    }

    // 用户登录 - 只使用Supabase认证
    async login(email, password) {
        console.log('🔐 [UNIFIED-AUTH] 开始登录:', email);
        
        // 确保初始化完成
        if (!this.isInitialized) {
            await this.initPromise;
        }

        try {
            // 只使用Supabase数据库登录
            if (this.supabase) {
                return await this.loginWithSupabase(email, password);
            } else {
                return { success: false, message: '数据库连接不可用，请稍后重试' };
            }

        } catch (error) {
            console.error('🔐 [UNIFIED-AUTH] 登录过程出错:', error);
            return { success: false, message: '登录失败，请稍后重试' };
        }
    }

    // 使用Supabase登录
    async loginWithSupabase(email, password) {
        try {
            console.log('🔐 [UNIFIED-AUTH] 尝试Supabase数据库登录');
            
            const { data: user, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('email', email)
                .eq('is_active', true)
                .single();

            if (error || !user) {
                console.error('🔐 [UNIFIED-AUTH] 用户查询失败:', error);
                return { success: false, message: '用户不存在或已被禁用' };
            }

            // 验证密码
            if (user.password_hash) {
                const hashedPassword = btoa(password + 'chunsheng_salt');
                if (hashedPassword !== user.password_hash) {
                    return { success: false, message: '密码错误' };
                }
            } else {
                return { success: false, message: '用户密码未设置，请联系管理员' };
            }

            this.setCurrentUser(user);
            console.log('🔐 [UNIFIED-AUTH] Supabase登录成功:', user.username);
            
            return {
                success: true,
                message: '登录成功！',
                user: user
            };

        } catch (error) {
            console.error('🔐 [UNIFIED-AUTH] Supabase登录出错:', error);
            return { success: false, message: '数据库登录失败' };
        }
    }

    // 设置当前用户
    setCurrentUser(user) {
        this.currentUser = user;
        this.userType = user.user_type || this.USER_TYPES.PREMIUM;
        
        // 保存到localStorage
        localStorage.setItem(this.STORAGE_KEYS.USER, JSON.stringify(user));
        localStorage.setItem(this.STORAGE_KEYS.LOGIN_TIME, Date.now().toString());
        localStorage.setItem(this.STORAGE_KEYS.SESSION_ID, this.generateSessionId());
        
        // 更新全局变量
        window.currentUser = this.currentUser;
        window.currentUserType = this.userType;
        
        console.log('🔐 [UNIFIED-AUTH] 用户状态已更新:', {
            username: user.username,
            type: this.userType
        });
    }

    // 用户注册
    async register(userData) {
        console.log('🔐 [UNIFIED-AUTH] 开始注册:', userData.email);
        
        if (!this.supabase) {
            return { success: false, message: '数据库连接不可用' };
        }

        try {
            // 检查用户是否已存在
            const { data: existingUser } = await this.supabase
                .from('users')
                .select('email')
                .eq('email', userData.email)
                .single();

            if (existingUser) {
                return { success: false, message: '该邮箱已被注册' };
            }

            // 创建新用户
            const newUser = {
                username: userData.username,
                email: userData.email,
                password_hash: btoa(userData.password + 'chunsheng_salt'),
                user_type: userData.user_type || this.USER_TYPES.PREMIUM,
                company_name: userData.company_name,
                phone: userData.phone,
                is_active: true
            };

            const { data, error } = await this.supabase
                .from('users')
                .insert([newUser])
                .select()
                .single();

            if (error) throw error;

            console.log('🔐 [UNIFIED-AUTH] 注册成功:', data.username);
            return { success: true, message: '注册成功！', user: data };

        } catch (error) {
            console.error('🔐 [UNIFIED-AUTH] 注册失败:', error);
            return { success: false, message: '注册失败，请稍后重试' };
        }
    }

    // 用户登出
    async logout() {
        console.log('🔐 [UNIFIED-AUTH] 用户登出');
        
        this.currentUser = null;
        this.userType = this.USER_TYPES.GUEST;
        
        // 清除存储
        this.clearAuthData();
        
        // 更新全局变量
        window.currentUser = null;
        window.currentUserType = this.USER_TYPES.GUEST;
        
        console.log('🔐 [UNIFIED-AUTH] 登出完成');
        return { success: true, message: '已成功登出' };
    }

    // 清除认证数据
    clearAuthData() {
        Object.values(this.STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 检查权限
    hasPermission(requiredType) {
        const typeHierarchy = {
            [this.USER_TYPES.GUEST]: 0,
            [this.USER_TYPES.PREMIUM]: 1,
            [this.USER_TYPES.PRIVILEGED]: 2,
            [this.USER_TYPES.ADMIN]: 3
        };

        const currentLevel = typeHierarchy[this.userType] || 0;
        const requiredLevel = typeHierarchy[requiredType] || 0;

        return currentLevel >= requiredLevel;
    }

    // 获取当前用户信息
    getCurrentUser() {
        return {
            user: this.currentUser,
            type: this.userType,
            isLoggedIn: this.currentUser !== null,
            permissions: {
                canViewProducts: this.hasPermission(this.USER_TYPES.GUEST),
                canDownloadBasic: this.hasPermission(this.USER_TYPES.PREMIUM),
                canDownloadPDF: this.hasPermission(this.USER_TYPES.PRIVILEGED),
                canManage: this.hasPermission(this.USER_TYPES.ADMIN)
            }
        };
    }
}

// 创建全局实例
console.log('🔐 [UNIFIED-AUTH] 创建统一认证系统实例');
window.unifiedAuth = new UnifiedAuth();

// 导出类（如果需要）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UnifiedAuth;
}