<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员RLS修复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .action-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            width: 200px;
        }
        .action-btn:hover {
            background-color: #0056b3;
        }
        .danger-btn {
            background-color: #dc3545;
        }
        .danger-btn:hover {
            background-color: #c82333;
        }
        .success-btn {
            background-color: #28a745;
        }
        .success-btn:hover {
            background-color: #218838;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 管理员RLS修复工具</h1>
        
        <div class="warning message" style="display: block;">
            <strong>问题说明：</strong>您的admin账户无法查看用户数据，这是因为RLS（行级安全）策略限制。<br>
            当前*****************账户没有对应的Supabase Auth记录，导致权限验证失败。
        </div>
        
        <h3>解决方案选择：</h3>
        
        <button class="action-btn success-btn" onclick="createAuthUser()">
            方案1: 为admin创建Auth记录
        </button>
        <p>为*****************创建对应的Supabase Auth用户记录</p>
        
        <button class="action-btn" onclick="updateRLSPolicy()">
            方案2: 更新RLS策略
        </button>
        <p>修改RLS策略以支持传统管理员登录</p>
        
        <button class="action-btn danger-btn" onclick="temporaryDisableRLS()">
            方案3: 临时禁用RLS
        </button>
        <p>⚠️ 临时禁用RLS（不推荐，仅用于紧急情况）</p>
        
        <button class="action-btn" onclick="checkCurrentStatus()">
            检查当前状态
        </button>
        <p>查看当前的用户和权限状态</p>
        
        <div id="message" class="message"></div>
        <div id="details" style="margin-top: 20px;"></div>
    </div>

    <!-- 引入 Supabase -->
    <script src="supabase-js.min.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        async function createAuthUser() {
            try {
                showMessage('正在为admin创建Auth记录...', 'info');
                
                // 使用service_role密钥创建用户
                const { data, error } = await supabase.auth.admin.createUser({
                    email: '<EMAIL>',
                    password: 'admin123',
                    email_confirm: true,
                    user_metadata: {
                        username: 'admin',
                        user_type: 'admin'
                    }
                });

                if (error) {
                    throw error;
                }

                showMessage('✅ Auth用户创建成功！现在admin账户可以正常使用了。', 'success');
                
            } catch (error) {
                console.error('创建Auth用户失败:', error);
                showMessage('❌ 创建失败: ' + error.message, 'error');
            }
        }

        async function updateRLSPolicy() {
            try {
                showMessage('正在更新RLS策略...', 'info');
                
                // 删除现有的admin策略
                await supabase.rpc('execute_sql', {
                    sql: `
                        DROP POLICY IF EXISTS "admin_full_access" ON public.users;
                        DROP POLICY IF EXISTS "admin_local_full_access" ON public.users;
                        DROP POLICY IF EXISTS "admin_type_full_access" ON public.users;
                        
                        -- 创建新的管理员策略
                        CREATE POLICY "admin_universal_access" ON public.users 
                        FOR ALL 
                        USING (
                            auth.uid() = '4a799fe1-27e1-4e5b-847d-3b03a7003080'::uuid OR
                            auth.uid() = '34252405-c3cc-44e8-bee6-15e6f6aea660'::uuid OR
                            EXISTS (
                                SELECT 1 FROM public.users u 
                                WHERE u.id = auth.uid() AND u.user_type = 'admin'
                            )
                        );
                    `
                });

                showMessage('✅ RLS策略更新成功！', 'success');
                
            } catch (error) {
                console.error('更新RLS策略失败:', error);
                showMessage('❌ 更新失败: ' + error.message, 'error');
            }
        }

        async function temporaryDisableRLS() {
            if (!confirm('确定要临时禁用RLS吗？这会降低数据安全性。')) {
                return;
            }
            
            try {
                showMessage('正在临时禁用RLS...', 'info');
                
                await supabase.rpc('execute_sql', {
                    sql: 'ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;'
                });

                showMessage('⚠️ RLS已临时禁用。请在解决问题后重新启用！', 'warning');
                
            } catch (error) {
                console.error('禁用RLS失败:', error);
                showMessage('❌ 操作失败: ' + error.message, 'error');
            }
        }

        async function checkCurrentStatus() {
            try {
                showMessage('正在检查当前状态...', 'info');
                
                // 检查当前用户
                const { data: { user } } = await supabase.auth.getUser();
                
                // 检查RLS状态
                const { data: rlsStatus } = await supabase
                    .from('pg_tables')
                    .select('rowsecurity')
                    .eq('tablename', 'users')
                    .eq('schemaname', 'public')
                    .single();

                // 尝试查询用户数据
                const { data: users, error: usersError } = await supabase
                    .from('users')
                    .select('count');

                let statusHtml = '<h3>当前状态：</h3>';
                statusHtml += `<p><strong>当前Auth用户:</strong> ${user ? user.email : '未登录'}</p>`;
                statusHtml += `<p><strong>RLS状态:</strong> ${rlsStatus?.rowsecurity ? '启用' : '禁用'}</p>`;
                statusHtml += `<p><strong>用户数据访问:</strong> ${usersError ? '❌ 失败: ' + usersError.message : '✅ 成功'}</p>`;

                document.getElementById('details').innerHTML = statusHtml;
                showMessage('状态检查完成', 'success');
                
            } catch (error) {
                console.error('检查状态失败:', error);
                showMessage('❌ 检查失败: ' + error.message, 'error');
            }
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            setTimeout(checkCurrentStatus, 1000);
        });
    </script>
</body>
</html>
