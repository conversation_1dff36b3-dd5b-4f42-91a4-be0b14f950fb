<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase 数据备份工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 400px;
            border: 1px solid #e9ecef;
        }
        .backup-section {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .table-info {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .table-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Supabase 数据备份工具</h1>
        
        <div class="warning">
            <h3>⚠️ 重要提醒</h3>
            <p>您的Supabase账号已丢失，但项目仍在运行。请立即备份所有数据！</p>
            <p><strong>当前Supabase项目：</strong> snckktsqwrbfwtjlvcfr.supabase.co</p>
        </div>

        <div class="backup-section">
            <h3>📊 数据库状态检查</h3>
            <button onclick="checkDatabaseStatus()">检查数据库连接</button>
            <button onclick="getTableList()">获取表列表</button>
            <div id="db-status"></div>
        </div>

        <div class="backup-section">
            <h3>💾 数据备份操作</h3>
            <button onclick="backupAllData()" class="success">备份所有数据</button>
            <button onclick="backupUsers()">备份用户数据</button>
            <button onclick="backupProducts()">备份产品数据</button>
            <button onclick="backupCustomerService()">备份客服数据</button>
            <div class="progress" id="backup-progress" style="display: none;">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            <div id="backup-status"></div>
        </div>

        <div class="backup-section">
            <h3>📁 配置备份</h3>
            <button onclick="backupConfig()">备份Supabase配置</button>
            <button onclick="generateMigrationSQL()">生成迁移SQL</button>
            <div id="config-backup"></div>
        </div>

        <div class="backup-section">
            <h3>🔄 数据恢复准备</h3>
            <button onclick="generateRestoreScript()">生成恢复脚本</button>
            <button onclick="downloadAllBackups()" class="success">下载完整备份包</button>
            <div id="restore-info"></div>
        </div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 初始化 Supabase 客户端
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        let backupData = {};
        let tableList = [];

        function showMessage(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function updateProgress(percent) {
            const progressBar = document.getElementById('progress-bar');
            const progressContainer = document.getElementById('backup-progress');
            progressContainer.style.display = 'block';
            progressBar.style.width = percent + '%';
        }

        async function checkDatabaseStatus() {
            try {
                showMessage('db-status', '正在检查数据库连接...', 'info');
                
                // 测试基本连接
                const { data, error } = await supabase
                    .from('users')
                    .select('count', { count: 'exact', head: true });
                
                if (error) {
                    showMessage('db-status', `❌ 数据库连接失败: ${error.message}`, 'error');
                } else {
                    showMessage('db-status', `✅ 数据库连接正常，用户表有 ${data || '未知'} 条记录`, 'success');
                }
            } catch (error) {
                showMessage('db-status', `❌ 连接异常: ${error.message}`, 'error');
            }
        }

        async function getTableList() {
            try {
                showMessage('db-status', '正在获取表列表...', 'info');
                
                // 尝试查询已知的表
                const knownTables = ['users', 'products', 'customer_service_messages', 'download_records'];
                const tableInfo = [];
                
                for (const tableName of knownTables) {
                    try {
                        const { data, error } = await supabase
                            .from(tableName)
                            .select('*', { count: 'exact', head: true });
                        
                        if (!error) {
                            tableInfo.push({
                                name: tableName,
                                count: data || 0,
                                status: '✅ 可访问'
                            });
                        } else {
                            tableInfo.push({
                                name: tableName,
                                count: 0,
                                status: '❌ 无法访问'
                            });
                        }
                    } catch (e) {
                        tableInfo.push({
                            name: tableName,
                            count: 0,
                            status: '❌ 错误'
                        });
                    }
                }
                
                tableList = tableInfo;
                
                const tableHtml = `
                    <div class="table-info">
                        ${tableInfo.map(table => `
                            <div class="table-card">
                                <strong>${table.name}</strong><br>
                                记录数: ${table.count}<br>
                                状态: ${table.status}
                            </div>
                        `).join('')}
                    </div>
                `;
                
                showMessage('db-status', `📋 数据库表信息：${tableHtml}`, 'success');
            } catch (error) {
                showMessage('db-status', `❌ 获取表列表失败: ${error.message}`, 'error');
            }
        }

        async function backupUsers() {
            try {
                showMessage('backup-status', '正在备份用户数据...', 'info');
                updateProgress(10);
                
                const { data, error } = await supabase
                    .from('users')
                    .select('*');
                
                if (error) throw error;
                
                backupData.users = data;
                updateProgress(100);
                
                // 下载用户数据
                downloadJSON(data, 'users_backup.json');
                showMessage('backup-status', `✅ 用户数据备份完成，共 ${data.length} 条记录`, 'success');
            } catch (error) {
                showMessage('backup-status', `❌ 用户数据备份失败: ${error.message}`, 'error');
            }
        }

        async function backupProducts() {
            try {
                showMessage('backup-status', '正在备份产品数据...', 'info');
                updateProgress(10);
                
                const { data, error } = await supabase
                    .from('products')
                    .select('*');
                
                if (error) throw error;
                
                backupData.products = data;
                updateProgress(100);
                
                downloadJSON(data, 'products_backup.json');
                showMessage('backup-status', `✅ 产品数据备份完成，共 ${data.length} 条记录`, 'success');
            } catch (error) {
                showMessage('backup-status', `❌ 产品数据备份失败: ${error.message}`, 'error');
            }
        }

        async function backupCustomerService() {
            try {
                showMessage('backup-status', '正在备份客服数据...', 'info');
                updateProgress(10);
                
                const { data, error } = await supabase
                    .from('customer_service_messages')
                    .select('*');
                
                if (error) throw error;
                
                backupData.customer_service_messages = data;
                updateProgress(100);
                
                downloadJSON(data, 'customer_service_backup.json');
                showMessage('backup-status', `✅ 客服数据备份完成，共 ${data.length} 条记录`, 'success');
            } catch (error) {
                showMessage('backup-status', `❌ 客服数据备份失败: ${error.message}`, 'error');
            }
        }

        async function backupAllData() {
            try {
                showMessage('backup-status', '正在备份所有数据...', 'info');
                updateProgress(0);
                
                const tables = ['users', 'products', 'customer_service_messages', 'download_records'];
                const allData = {};
                
                for (let i = 0; i < tables.length; i++) {
                    const tableName = tables[i];
                    updateProgress((i / tables.length) * 80);
                    
                    try {
                        const { data, error } = await supabase
                            .from(tableName)
                            .select('*');
                        
                        if (!error) {
                            allData[tableName] = data;
                            console.log(`✅ ${tableName}: ${data.length} 条记录`);
                        } else {
                            console.warn(`⚠️ ${tableName}: ${error.message}`);
                            allData[tableName] = [];
                        }
                    } catch (e) {
                        console.error(`❌ ${tableName}: ${e.message}`);
                        allData[tableName] = [];
                    }
                }
                
                updateProgress(90);
                backupData = allData;
                
                // 添加备份元信息
                const backupInfo = {
                    backup_date: new Date().toISOString(),
                    supabase_url: 'https://snckktsqwrbfwtjlvcfr.supabase.co',
                    tables: Object.keys(allData),
                    total_records: Object.values(allData).reduce((sum, table) => sum + table.length, 0)
                };
                
                const completeBackup = {
                    backup_info: backupInfo,
                    data: allData
                };
                
                updateProgress(100);
                downloadJSON(completeBackup, `chunsheng_complete_backup_${new Date().toISOString().split('T')[0]}.json`);
                
                const summary = Object.entries(allData)
                    .map(([table, data]) => `${table}: ${data.length} 条记录`)
                    .join('<br>');
                
                showMessage('backup-status', `✅ 完整备份完成！<br>${summary}`, 'success');
            } catch (error) {
                showMessage('backup-status', `❌ 完整备份失败: ${error.message}`, 'error');
            }
        }

        function backupConfig() {
            const config = {
                supabase_url: 'https://snckktsqwrbfwtjlvcfr.supabase.co',
                anon_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644',
                project_ref: 'snckktsqwrbfwtjlvcfr',
                backup_date: new Date().toISOString(),
                note: '原Supabase账号已丢失，此为配置备份'
            };
            
            downloadJSON(config, 'supabase_config_backup.json');
            showMessage('config-backup', '✅ Supabase配置已备份', 'success');
        }

        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 页面加载时自动检查状态
        window.addEventListener('load', function() {
            setTimeout(checkDatabaseStatus, 1000);
        });
    </script>
</body>
</html>
