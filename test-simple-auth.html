<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试简化认证系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .user-status {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 简化认证系统测试</h1>
        
        <div class="user-status" id="userStatus">
            <h3>当前状态：</h3>
            <p id="statusText">检查中...</p>
        </div>
        
        <div class="test-section">
            <h3>测试登录</h3>
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456" placeholder="输入密码">
            </div>
            <button onclick="testLogin()">测试登录</button>
            <button onclick="logout()" style="background-color: #dc3545;">登出</button>
        </div>
        
        <div class="test-section">
            <h3>预设测试账户</h3>
            <button onclick="loginAs('<EMAIL>', '123456')">登录为 tex (特许用户)</button>
            <button onclick="loginAs('<EMAIL>', 'admin123')">登录为 admin (管理员)</button>
            <button onclick="loginAs('<EMAIL>', 'admin123456')">登录为 liangbu (管理员)</button>
        </div>
        
        <div class="test-section">
            <h3>权限测试</h3>
            <button onclick="testPermissions()">测试当前用户权限</button>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- 引入 Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/simple-auth.js"></script>
    
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = type;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
            
            // 滚动到结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }

        function updateUserStatus() {
            const statusText = document.getElementById('statusText');
            const currentUser = window.simpleAuth.getCurrentUser();
            
            if (currentUser) {
                statusText.innerHTML = `
                    <strong>已登录</strong><br>
                    用户名: ${currentUser.username}<br>
                    邮箱: ${currentUser.email}<br>
                    权限: ${window.simpleAuth.getUserTypeDisplay()}<br>
                    公司: ${currentUser.company_name || '未填写'}
                `;
            } else {
                statusText.innerHTML = '<strong>未登录</strong> (游客状态)';
            }
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showResult('请输入邮箱和密码', 'error');
                return;
            }
            
            try {
                showResult(`正在测试登录: ${email}`, 'info');
                
                const result = await window.simpleAuth.loginUser(email, password);
                
                if (result.success) {
                    showResult(`✅ 登录成功！<br>
                        用户: ${result.user.username}<br>
                        权限: ${result.user.user_type}<br>
                        邮箱: ${result.user.email}`, 'success');
                    updateUserStatus();
                } else {
                    showResult(`❌ 登录失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                console.error('登录测试失败:', error);
                showResult(`❌ 登录测试异常: ${error.message}`, 'error');
            }
        }

        async function loginAs(email, password) {
            try {
                showResult(`正在登录为: ${email}`, 'info');
                
                const result = await window.simpleAuth.loginUser(email, password);
                
                if (result.success) {
                    showResult(`✅ 登录成功！用户: ${result.user.username} (${result.user.user_type})`, 'success');
                    updateUserStatus();
                } else {
                    showResult(`❌ 登录失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                console.error('登录失败:', error);
                showResult(`❌ 登录异常: ${error.message}`, 'error');
            }
        }

        function logout() {
            const result = window.simpleAuth.logout();
            showResult(`✅ ${result.message}`, 'success');
            updateUserStatus();
        }

        function testPermissions() {
            const currentUser = window.simpleAuth.getCurrentUser();
            
            if (!currentUser) {
                showResult('❌ 请先登录', 'error');
                return;
            }
            
            const permissions = {
                '查看详情': window.simpleAuth.canViewDetails(),
                '下载PDF': window.simpleAuth.canDownload(),
                '下载基础资料': window.simpleAuth.canDownloadBasic(),
                '管理员权限': window.simpleAuth.isAdmin()
            };
            
            let permissionText = `<strong>${currentUser.username} 的权限测试结果：</strong><br>`;
            for (const [perm, hasPermission] of Object.entries(permissions)) {
                permissionText += `${perm}: ${hasPermission ? '✅ 有权限' : '❌ 无权限'}<br>`;
            }
            
            showResult(permissionText, 'info');
        }

        // 页面加载时更新状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateUserStatus();
                showResult('✅ 简化认证系统测试页面加载完成', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
