<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Supabase修复测试</h1>
        
        <div class="status" id="status">
            <h3>系统状态检查：</h3>
            <div id="statusContent">检查中...</div>
        </div>
        
        <div class="test-section">
            <h3>基础功能测试</h3>
            <button onclick="testSupabaseClient()">测试Supabase客户端</button>
            <button onclick="testDatabaseQuery()">测试数据库查询</button>
            <button onclick="testTexUser()">测试tex用户查询</button>
        </div>
        
        <div class="test-section">
            <h3>认证功能测试</h3>
            <button onclick="testLogin()">测试登录功能</button>
            <button onclick="testPasswordHash()">测试密码哈希</button>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- 引入修复后的脚本 -->
    <script src="supabase-simple.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = type;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
            
            // 滚动到结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }

        function updateStatus() {
            const statusContent = document.getElementById('statusContent');
            
            let status = '';
            status += `window.supabase 存在: ${typeof window.supabase !== 'undefined' ? '✅' : '❌'}\n`;
            status += `window.supabaseClient 存在: ${typeof window.supabaseClient !== 'undefined' ? '✅' : '❌'}\n`;
            status += `createClient 函数: ${typeof window.supabase?.createClient === 'function' ? '✅' : '❌'}\n`;
            status += `from 方法: ${typeof window.supabaseClient?.from === 'function' ? '✅' : '❌'}\n`;
            
            if (typeof window.supabaseClient !== 'undefined') {
                const testTable = window.supabaseClient.from('users');
                status += `表查询对象: ${typeof testTable === 'object' ? '✅' : '❌'}\n`;
                status += `select 方法: ${typeof testTable.select === 'function' ? '✅' : '❌'}\n`;
            }
            
            statusContent.textContent = status;
        }

        function testSupabaseClient() {
            try {
                showResult('正在测试Supabase客户端...', 'info');
                
                if (typeof window.supabase === 'undefined') {
                    throw new Error('window.supabase 未定义');
                }
                
                if (typeof window.supabaseClient === 'undefined') {
                    throw new Error('window.supabaseClient 未定义');
                }
                
                if (typeof window.supabaseClient.from !== 'function') {
                    throw new Error('supabaseClient.from 不是函数');
                }
                
                const testTable = window.supabaseClient.from('users');
                if (typeof testTable.select !== 'function') {
                    throw new Error('table.select 不是函数');
                }
                
                showResult('✅ Supabase客户端测试通过！所有基础功能正常', 'success');
                
            } catch (error) {
                showResult(`❌ Supabase客户端测试失败: ${error.message}`, 'error');
            }
        }

        async function testDatabaseQuery() {
            try {
                showResult('正在测试数据库查询...', 'info');
                
                const { data, error } = await window.supabaseClient
                    .from('users')
                    .select('id, email, username')
                    .limit(3)
                    .execute();
                
                if (error) {
                    throw error;
                }
                
                showResult(`✅ 数据库查询成功！返回 ${data.length} 条记录`, 'success');
                
            } catch (error) {
                showResult(`❌ 数据库查询失败: ${error.message}`, 'error');
            }
        }

        async function testTexUser() {
            try {
                showResult('正在查询tex用户...', 'info');
                
                const { data: user, error } = await window.supabaseClient
                    .from('users')
                    .select('*')
                    .eq('email', '<EMAIL>')
                    .single();
                
                if (error) {
                    throw error;
                }
                
                if (user) {
                    showResult(`✅ tex用户查询成功！<br>
                        用户名: ${user.username}<br>
                        邮箱: ${user.email}<br>
                        权限: ${user.user_type}<br>
                        密码哈希: ${user.password_hash ? '已设置' : '未设置'}<br>
                        状态: ${user.is_active ? '活跃' : '禁用'}`, 'success');
                } else {
                    showResult('❌ 未找到tex用户', 'error');
                }
                
            } catch (error) {
                showResult(`❌ tex用户查询失败: ${error.message}`, 'error');
            }
        }

        function testPasswordHash() {
            try {
                showResult('正在测试密码哈希...', 'info');
                
                const password = '123456';
                const hashedPassword = btoa(password + 'chunsheng_salt');
                
                showResult(`✅ 密码哈希测试成功！<br>
                    原密码: ${password}<br>
                    哈希值: ${hashedPassword}`, 'success');
                
            } catch (error) {
                showResult(`❌ 密码哈希测试失败: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                showResult('正在测试登录功能...', 'info');
                
                const result = await loginUser('<EMAIL>', '123456');
                
                if (result.success) {
                    showResult(`✅ 登录功能测试成功！<br>
                        用户: ${result.user.username}<br>
                        权限: ${result.user.user_type}`, 'success');
                } else {
                    showResult(`❌ 登录功能测试失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                showResult(`❌ 登录功能测试异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateStatus();
                showResult('🔧 Supabase修复测试页面已加载', 'info');
                showResult('📋 修复内容：<br>• 替换了有问题的 supabase-js.min.js<br>• 创建了简化的Supabase客户端<br>• 支持完整的数据库操作', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
