# 产品文件重命名工具 PowerShell版本
# 用于批量重命名图片和PDF文件

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "          产品文件重命名工具" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "此工具将帮助您批量重命名图片和PDF文件" -ForegroundColor Yellow
Write-Host "重命名格式：" -ForegroundColor Yellow
Write-Host "  图片: A0014.jpg, A0015.png 等" -ForegroundColor Green
Write-Host "  PDF:  A0014_技术文档.pdf, A0015_说明书.pdf 等" -ForegroundColor Green
Write-Host ""

# 获取源目录
$sourceDir = Read-Host "请输入文件所在目录路径（直接回车使用当前目录）"
if ([string]::IsNullOrWhiteSpace($sourceDir)) {
    $sourceDir = "."
}

if (!(Test-Path $sourceDir)) {
    Write-Host "错误：目录 $sourceDir 不存在" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit
}

Write-Host ""
Write-Host "开始处理目录: $sourceDir" -ForegroundColor Cyan
Write-Host ""

# 数据ID列表
$dataIds = @(
    "A0014", "A0015", "A0016", "A0017", "A0018",
    "A0004", "A0005", "A0006", "A0007", "A0008", 
    "A0009", "A0010", "A0011", "A0012", "A0013"
)

# PDF后缀列表
$pdfSuffixes = @("技术文档", "说明书", "产品手册", "工艺文档", "检验标准")

# 获取所有图片文件
$imageExtensions = @("*.jpg", "*.jpeg", "*.png", "*.gif", "*.bmp")
$imageFiles = Get-ChildItem -Path $sourceDir -Include $imageExtensions -File

# 获取所有PDF文件
$pdfFiles = Get-ChildItem -Path $sourceDir -Filter "*.pdf" -File

Write-Host "找到 $($imageFiles.Count) 个图片文件" -ForegroundColor Yellow
Write-Host "找到 $($pdfFiles.Count) 个PDF文件" -ForegroundColor Yellow
Write-Host "可用数据ID: $($dataIds -join ', ')" -ForegroundColor Yellow
Write-Host ""

# 重命名图片文件
if ($imageFiles.Count -gt 0) {
    Write-Host "=== 重命名图片文件 ===" -ForegroundColor Cyan
    
    for ($i = 0; $i -lt $imageFiles.Count -and $i -lt $dataIds.Count; $i++) {
        $file = $imageFiles[$i]
        $dataId = $dataIds[$i]
        $extension = $file.Extension.ToLower()
        $newName = "$dataId$extension"
        $newPath = Join-Path $sourceDir $newName
        
        try {
            Copy-Item -Path $file.FullName -Destination $newPath -Force
            Write-Host "✅ $($file.Name) -> $newName" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ 重命名失败 $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 处理超出数据ID数量的文件
    for ($i = $dataIds.Count; $i -lt $imageFiles.Count; $i++) {
        $file = $imageFiles[$i]
        Write-Host "⚠️  跳过 $($file.Name) (数据ID不足)" -ForegroundColor Yellow
    }
}

Write-Host ""

# 重命名PDF文件
if ($pdfFiles.Count -gt 0) {
    Write-Host "=== 重命名PDF文件 ===" -ForegroundColor Cyan
    
    for ($i = 0; $i -lt $pdfFiles.Count -and $i -lt $dataIds.Count; $i++) {
        $file = $pdfFiles[$i]
        $dataId = $dataIds[$i]
        $suffix = $pdfSuffixes[$i % $pdfSuffixes.Count]
        $newName = "${dataId}_${suffix}.pdf"
        $newPath = Join-Path $sourceDir $newName
        
        try {
            Copy-Item -Path $file.FullName -Destination $newPath -Force
            Write-Host "✅ $($file.Name) -> $newName" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ 重命名失败 $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 处理超出数据ID数量的文件
    for ($i = $dataIds.Count; $i -lt $pdfFiles.Count; $i++) {
        $file = $pdfFiles[$i]
        Write-Host "⚠️  跳过 $($file.Name) (数据ID不足)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "=== 重命名完成 ===" -ForegroundColor Cyan
Write-Host "重命名后的文件已保存在同一目录中" -ForegroundColor Green
Write-Host "原文件保持不变" -ForegroundColor Green
Write-Host ""
Read-Host "按回车键退出"
