<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品数据调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .error { background: #ffebee; color: #d32f2f; }
        .success { background: #e8f5e8; color: #388e3c; }
        .info { background: #e3f2fd; color: #1976d2; }
        button { background: #be131b; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>产品数据调试页面</h1>
    
    <button onclick="testSupabaseConnection()">测试 Supabase 连接</button>
    <button onclick="testGetProducts()">测试 getProducts 函数</button>
    <button onclick="testDirectQuery()">直接查询数据库</button>
    <button onclick="clearLogs()">清除日志</button>
    
    <div id="logs"></div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logs.appendChild(div);
            console.log(message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        async function testSupabaseConnection() {
            log('开始测试 Supabase 连接...', 'info');
            
            try {
                log(`Supabase URL: ${SUPABASE_URL}`, 'info');
                log(`Supabase 客户端: ${typeof supabase}`, 'info');
                
                if (typeof supabase === 'undefined') {
                    log('❌ Supabase 客户端未定义', 'error');
                    return;
                }
                
                log('✅ Supabase 客户端已初始化', 'success');
                
                // 测试简单查询
                const { data, error } = await supabase
                    .from('products')
                    .select('count')
                    .limit(1);
                
                if (error) {
                    log(`❌ 数据库连接失败: ${error.message}`, 'error');
                    log(`错误详情: ${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    log('✅ 数据库连接成功', 'success');
                }
                
            } catch (error) {
                log(`❌ 连接测试异常: ${error.message}`, 'error');
            }
        }
        
        async function testGetProducts() {
            log('开始测试 getProducts 函数...', 'info');
            
            try {
                if (typeof getProducts === 'undefined') {
                    log('❌ getProducts 函数未定义', 'error');
                    return;
                }
                
                log('调用 getProducts()...', 'info');
                const products = await getProducts();
                
                log(`✅ getProducts 返回结果: ${products ? products.length : 0} 个产品`, 'success');
                
                if (products && products.length > 0) {
                    log(`第一个产品: ${JSON.stringify(products[0], null, 2)}`, 'info');
                } else {
                    log('❌ 没有获取到产品数据', 'error');
                }
                
            } catch (error) {
                log(`❌ getProducts 测试异常: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
            }
        }
        
        async function testDirectQuery() {
            log('开始直接查询数据库...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('products')
                    .select('*')
                    .limit(5);
                
                if (error) {
                    log(`❌ 直接查询失败: ${error.message}`, 'error');
                    log(`错误详情: ${JSON.stringify(error, null, 2)}`, 'error');
                } else {
                    log(`✅ 直接查询成功: ${data ? data.length : 0} 个产品`, 'success');
                    
                    if (data && data.length > 0) {
                        data.forEach((product, index) => {
                            log(`产品 ${index + 1}: ${product.product_name} (${product.data_id})`, 'info');
                        });
                    }
                }
                
            } catch (error) {
                log(`❌ 直接查询异常: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动测试...', 'info');
            setTimeout(testSupabaseConnection, 1000);
        });
    </script>
</body>
</html>
