#!/bin/bash

# 网站文件上传脚本
# 用于将本地网站文件上传到CentOS 8云主机

echo "=========================================="
echo "     网站文件上传工具"
echo "=========================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取用户输入
get_server_info() {
    echo "请提供服务器信息："
    read -p "服务器IP地址: " SERVER_IP
    read -p "SSH用户名 (默认: root): " SSH_USER
    read -p "服务器网站目录 (默认: /var/www/chunsheng): " REMOTE_DIR
    read -p "本地网站目录 (默认: ./chunsheng-website): " LOCAL_DIR
    
    # 设置默认值
    SSH_USER=${SSH_USER:-root}
    REMOTE_DIR=${REMOTE_DIR:-/var/www/chunsheng}
    LOCAL_DIR=${LOCAL_DIR:-./chunsheng-website}
    
    echo ""
    log_info "配置信息："
    echo "服务器IP: $SERVER_IP"
    echo "SSH用户: $SSH_USER"
    echo "远程目录: $REMOTE_DIR"
    echo "本地目录: $LOCAL_DIR"
    echo ""
}

# 检查本地文件
check_local_files() {
    if [[ ! -d "$LOCAL_DIR" ]]; then
        log_error "本地目录不存在: $LOCAL_DIR"
        exit 1
    fi
    
    local file_count=$(find "$LOCAL_DIR" -type f | wc -l)
    log_info "找到 $file_count 个文件待上传"
    
    # 显示主要文件
    echo "主要文件："
    ls -la "$LOCAL_DIR"/*.html 2>/dev/null || echo "  未找到HTML文件"
    ls -la "$LOCAL_DIR"/admin/ 2>/dev/null | head -3 || echo "  未找到admin目录"
    ls -la "$LOCAL_DIR"/js/ 2>/dev/null | head -3 || echo "  未找到js目录"
    echo ""
}

# 测试SSH连接
test_ssh_connection() {
    log_info "测试SSH连接..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes "$SSH_USER@$SERVER_IP" exit 2>/dev/null; then
        log_success "SSH连接测试成功"
    else
        log_warning "SSH连接测试失败，请确保："
        echo "1. 服务器IP地址正确"
        echo "2. SSH密钥已配置或准备输入密码"
        echo "3. 服务器SSH服务正常运行"
        echo ""
        read -p "是否继续? (y/n): " CONTINUE
        if [[ $CONTINUE != "y" ]]; then
            exit 1
        fi
    fi
}

# 创建远程目录
create_remote_directory() {
    log_info "创建远程目录..."
    ssh "$SSH_USER@$SERVER_IP" "mkdir -p $REMOTE_DIR"
    log_success "远程目录创建完成"
}

# 备份远程文件
backup_remote_files() {
    log_info "备份远程文件..."
    local backup_dir="${REMOTE_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
    ssh "$SSH_USER@$SERVER_IP" "
        if [[ -d '$REMOTE_DIR' ]] && [[ \$(ls -A '$REMOTE_DIR' 2>/dev/null) ]]; then
            cp -r '$REMOTE_DIR' '$backup_dir'
            echo '备份创建: $backup_dir'
        else
            echo '无需备份（目录为空或不存在）'
        fi
    "
    log_success "备份完成"
}

# 上传文件
upload_files() {
    log_info "开始上传文件..."
    
    # 使用rsync上传（推荐）
    if command -v rsync &> /dev/null; then
        log_info "使用rsync上传文件..."
        rsync -avz --progress --delete \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='*.log' \
            --exclude='.DS_Store' \
            "$LOCAL_DIR/" "$SSH_USER@$SERVER_IP:$REMOTE_DIR/"
        
        if [[ $? -eq 0 ]]; then
            log_success "文件上传完成"
        else
            log_error "rsync上传失败，尝试使用scp..."
            upload_with_scp
        fi
    else
        log_warning "rsync未安装，使用scp上传..."
        upload_with_scp
    fi
}

# 使用SCP上传
upload_with_scp() {
    log_info "使用scp上传文件..."
    scp -r "$LOCAL_DIR"/* "$SSH_USER@$SERVER_IP:$REMOTE_DIR/"
    
    if [[ $? -eq 0 ]]; then
        log_success "文件上传完成"
    else
        log_error "文件上传失败"
        exit 1
    fi
}

# 设置远程文件权限
set_remote_permissions() {
    log_info "设置文件权限..."
    ssh "$SSH_USER@$SERVER_IP" "
        chown -R nginx:nginx '$REMOTE_DIR'
        chmod -R 755 '$REMOTE_DIR'
        find '$REMOTE_DIR' -type f -exec chmod 644 {} \;
        find '$REMOTE_DIR' -type d -exec chmod 755 {} \;
    "
    log_success "文件权限设置完成"
}

# 重启Nginx
restart_nginx() {
    log_info "重启Nginx服务..."
    ssh "$SSH_USER@$SERVER_IP" "systemctl restart nginx"
    
    if [[ $? -eq 0 ]]; then
        log_success "Nginx重启成功"
    else
        log_warning "Nginx重启失败，请手动检查"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查文件是否存在
    ssh "$SSH_USER@$SERVER_IP" "
        echo '检查主要文件:'
        ls -la '$REMOTE_DIR'/index.html 2>/dev/null && echo '✅ index.html 存在' || echo '❌ index.html 不存在'
        ls -la '$REMOTE_DIR'/admin/ 2>/dev/null && echo '✅ admin目录 存在' || echo '❌ admin目录 不存在'
        ls -la '$REMOTE_DIR'/js/ 2>/dev/null && echo '✅ js目录 存在' || echo '❌ js目录 不存在'
        ls -la '$REMOTE_DIR'/css/ 2>/dev/null && echo '✅ css目录 存在' || echo '❌ css目录 不存在'
        
        echo ''
        echo '检查Nginx状态:'
        systemctl is-active nginx && echo '✅ Nginx 运行中' || echo '❌ Nginx 未运行'
        
        echo ''
        echo '检查端口监听:'
        ss -tlnp | grep :80 && echo '✅ 端口80 监听中' || echo '❌ 端口80 未监听'
    "
}

# 显示完成信息
show_completion_info() {
    echo ""
    echo "=========================================="
    log_success "部署完成！"
    echo "=========================================="
    echo ""
    echo "部署信息："
    echo "  服务器: $SERVER_IP"
    echo "  网站目录: $REMOTE_DIR"
    echo "  上传时间: $(date)"
    echo ""
    echo "访问网站："
    echo "  HTTP: http://$SERVER_IP"
    echo "  管理后台: http://$SERVER_IP/admin/"
    echo ""
    echo "常用命令（在服务器上执行）："
    echo "  查看网站文件: ls -la $REMOTE_DIR"
    echo "  查看Nginx状态: systemctl status nginx"
    echo "  查看访问日志: tail -f /var/log/nginx/access.log"
    echo "  重启Nginx: systemctl restart nginx"
    echo ""
}

# 主函数
main() {
    get_server_info
    check_local_files
    
    read -p "确认开始上传? (y/n): " CONFIRM
    if [[ $CONFIRM != "y" ]]; then
        log_error "上传已取消"
        exit 1
    fi
    
    test_ssh_connection
    create_remote_directory
    backup_remote_files
    upload_files
    set_remote_permissions
    restart_nginx
    verify_deployment
    show_completion_info
}

# 运行主函数
main "$@"
