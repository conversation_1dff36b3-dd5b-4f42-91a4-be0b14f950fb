<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase 数据库结构与RLS策略可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .mindmap {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .root-node {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px 40px;
            border-radius: 50px;
            font-size: 1.5rem;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .root-node:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .main-branches {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            width: 100%;
        }

        .branch {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .branch:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .branch.auth { border-left-color: #e53e3e; }
        .branch.public { border-left-color: #38a169; }
        .branch.rls { border-left-color: #3182ce; }
        .branch.permissions { border-left-color: #d69e2e; }
        .branch.issues { border-left-color: #805ad5; }

        .branch-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3748;
        }

        .table-item {
            background: #f7fafc;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 3px solid #cbd5e0;
            transition: all 0.3s ease;
        }

        .table-item:hover {
            background: #edf2f7;
            border-left-color: #4299e1;
        }

        .table-name {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .table-details {
            display: none;
            margin-top: 10px;
        }

        .table-details.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .column {
            background: white;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 0.9rem;
            border-left: 2px solid #e2e8f0;
        }

        .column.primary { border-left-color: #f56565; }
        .column.foreign { border-left-color: #4299e1; }

        .policy {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .policy-name {
            font-weight: bold;
            color: #234e52;
        }

        .policy-rule {
            color: #2c7a7b;
            font-style: italic;
            margin-top: 5px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-enabled {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-disabled {
            background: #fed7d7;
            color: #742a2a;
        }

        .permission-level {
            background: #fff5f5;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
        }

        .permission-title {
            font-weight: bold;
            color: #742a2a;
            margin-bottom: 5px;
        }

        .permission-actions {
            color: #4a5568;
            font-size: 0.9rem;
        }

        .issue-item {
            background: #fef5e7;
            border: 1px solid #f6ad55;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
        }

        .issue-code {
            font-weight: bold;
            color: #c05621;
        }

        .issue-desc {
            color: #744210;
            margin-top: 5px;
        }

        .toggle-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background 0.3s ease;
        }

        .toggle-btn:hover {
            background: #3182ce;
        }

        .connection-line {
            position: absolute;
            background: #cbd5e0;
            z-index: -1;
        }

        @media (max-width: 768px) {
            .main-branches {
                grid-template-columns: 1fr;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Supabase 数据库结构与RLS策略</h1>
        
        <div class="mindmap">
            <div class="root-node" onclick="toggleAllBranches()">
                Supabase 数据库系统
            </div>
            
            <div class="main-branches">
                <!-- Auth Schema 分支 -->
                <div class="branch auth">
                    <div class="branch-title">🔐 Auth Schema</div>
                    <div class="table-item">
                        <div class="table-name">auth.users <span class="status-badge status-enabled">系统管理</span></div>
                        <button class="toggle-btn" onclick="toggleDetails(this)">显示详情</button>
                        <div class="table-details">
                            <div class="column primary">id: UUID (主键)</div>
                            <div class="column">email: VARCHAR</div>
                            <div class="column">raw_user_meta_data: JSONB</div>
                            <div class="column">created_at: TIMESTAMPTZ</div>
                            <div class="column">email_confirmed_at: TIMESTAMPTZ</div>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-name">auth.sessions</div>
                        <div style="color: #718096; font-size: 0.9rem;">会话管理表</div>
                    </div>
                    <div class="table-item">
                        <div class="table-name">auth.refresh_tokens</div>
                        <div style="color: #718096; font-size: 0.9rem;">令牌刷新表</div>
                    </div>
                </div>

                <!-- Public Schema 分支 -->
                <div class="branch public">
                    <div class="branch-title">🏢 Public Schema</div>
                    
                    <div class="table-item">
                        <div class="table-name">users <span class="status-badge status-enabled">RLS启用</span></div>
                        <button class="toggle-btn" onclick="toggleDetails(this)">显示详情</button>
                        <div class="table-details">
                            <div class="column primary">id: UUID (主键)</div>
                            <div class="column">username: VARCHAR(50)</div>
                            <div class="column">email: VARCHAR(100)</div>
                            <div class="column">first_name: VARCHAR(100)</div>
                            <div class="column">last_name: VARCHAR(100)</div>
                            <div class="column">user_type: VARCHAR(20) DEFAULT 'normal'</div>
                            <div class="column">company_name: VARCHAR(255)</div>
                            <div class="column">phone: VARCHAR(50)</div>
                            <div class="column">is_active: BOOLEAN DEFAULT true</div>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-name">products <span class="status-badge status-enabled">RLS启用</span></div>
                        <button class="toggle-btn" onclick="toggleDetails(this)">显示详情</button>
                        <div class="table-details">
                            <div class="column primary">id: INTEGER (主键)</div>
                            <div class="column">stock_code: VARCHAR</div>
                            <div class="column">product_name: VARCHAR</div>
                            <div class="column">product_category: VARCHAR</div>
                            <div class="column">material: VARCHAR</div>
                            <div class="column">attachment_path: TEXT</div>
                            <div class="column">data_id: VARCHAR</div>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-name">customer_service_messages <span class="status-badge status-enabled">RLS启用</span></div>
                        <button class="toggle-btn" onclick="toggleDetails(this)">显示详情</button>
                        <div class="table-details">
                            <div class="column primary">id: INTEGER (主键)</div>
                            <div class="column foreign">user_id: INTEGER ⚠️ 类型不匹配</div>
                            <div class="column">user_name: VARCHAR</div>
                            <div class="column">message_content: TEXT</div>
                            <div class="column">message_type: VARCHAR DEFAULT 'user'</div>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-name">download_records <span class="status-badge status-enabled">RLS启用</span></div>
                        <button class="toggle-btn" onclick="toggleDetails(this)">显示详情</button>
                        <div class="table-details">
                            <div class="column primary">id: INTEGER (主键)</div>
                            <div class="column foreign">user_id: INTEGER ⚠️ 类型不匹配</div>
                            <div class="column foreign">product_id: INTEGER</div>
                            <div class="column">file_type: VARCHAR</div>
                            <div class="column">downloaded_at: TIMESTAMPTZ</div>
                        </div>
                    </div>
                </div>

                <!-- RLS 策略分支 -->
                <div class="branch rls">
                    <div class="branch-title">🛡️ RLS 策略</div>

                    <div class="table-item">
                        <div class="table-name">users 表策略</div>
                        <div class="policy">
                            <div class="policy-name">users_select_own</div>
                            <div class="policy-rule">SELECT: auth.uid() = id</div>
                        </div>
                        <div class="policy">
                            <div class="policy-name">users_update_own</div>
                            <div class="policy-rule">UPDATE: auth.uid() = id</div>
                        </div>
                        <div class="policy">
                            <div class="policy-name">admin_full_access</div>
                            <div class="policy-rule">ALL: auth.uid() = '4a799fe1...'</div>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-name">products 表策略</div>
                        <div class="policy">
                            <div class="policy-name">Allow all users to view products</div>
                            <div class="policy-rule">SELECT: true</div>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-name">customer_service_messages 表策略</div>
                        <div class="policy">
                            <div class="policy-name">Allow guest messages</div>
                            <div class="policy-rule">INSERT: user_id IS NULL OR auth.uid() IS NOT NULL</div>
                        </div>
                        <div class="policy">
                            <div class="policy-name">Service role can manage all</div>
                            <div class="policy-rule">ALL: auth.role() = 'service_role'</div>
                        </div>
                    </div>
                </div>

                <!-- 权限级别分支 -->
                <div class="branch permissions">
                    <div class="branch-title">👥 权限级别</div>

                    <div class="permission-level">
                        <div class="permission-title">🚶 游客 (guest)</div>
                        <div class="permission-actions">
                            • 查看产品信息<br>
                            • 发送客服消息<br>
                            • 无需注册访问
                        </div>
                    </div>

                    <div class="permission-level">
                        <div class="permission-title">👤 普通用户 (normal)</div>
                        <div class="permission-actions">
                            • 查看和更新自己的数据<br>
                            • 记录下载历史<br>
                            • 发送客服消息<br>
                            • 查看产品详情
                        </div>
                    </div>

                    <div class="permission-level">
                        <div class="permission-title">👑 管理员 (admin)</div>
                        <div class="permission-actions">
                            • 完全访问权限<br>
                            • 管理所有用户<br>
                            • 管理产品信息<br>
                            • 处理客服消息<br>
                            • 查看系统统计
                        </div>
                    </div>
                </div>

                <!-- 问题诊断分支 -->
                <div class="branch issues">
                    <div class="branch-title">🔧 问题诊断</div>

                    <div class="issue-item">
                        <div class="issue-code">401 错误</div>
                        <div class="issue-desc">
                            认证失败，需要重新登录获取新的JWT令牌
                        </div>
                    </div>

                    <div class="issue-item">
                        <div class="issue-code">403 错误</div>
                        <div class="issue-desc">
                            权限不足，RLS策略阻止访问
                        </div>
                    </div>

                    <div class="issue-item">
                        <div class="issue-code">500 错误</div>
                        <div class="issue-desc">
                            策略递归或数据库内部错误
                        </div>
                    </div>

                    <div class="issue-item">
                        <div class="issue-code">⚠️ 数据类型不匹配</div>
                        <div class="issue-desc">
                            users.id (UUID) vs user_id (INTEGER) 在关联表中
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // ES6+ 交互功能
        class SupabaseMindMap {
            constructor() {
                this.initializeEventListeners();
                this.addConnectionLines();
                this.setupSearch();
            }

            initializeEventListeners() {
                // 为所有分支添加点击动画
                document.querySelectorAll('.branch').forEach(branch => {
                    branch.addEventListener('click', (e) => {
                        if (e.target.classList.contains('toggle-btn')) return;
                        this.animateBranch(branch);
                    });
                });

                // 添加键盘导航
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.closeAllDetails();
                    }
                    if (e.key === 'Enter' && e.target.type === 'text') {
                        this.searchMindMap(e.target.value);
                    }
                });
            }

            animateBranch(branch) {
                branch.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    branch.style.transform = 'translateY(-5px)';
                }, 100);
                setTimeout(() => {
                    branch.style.transform = '';
                }, 300);
            }

            addConnectionLines() {
                // 添加视觉连接线（简化版）
                const rootNode = document.querySelector('.root-node');
                const branches = document.querySelectorAll('.branch');

                console.log('连接线功能已初始化');
            }

            closeAllDetails() {
                document.querySelectorAll('.table-details.show').forEach(detail => {
                    detail.classList.remove('show');
                });
                document.querySelectorAll('.toggle-btn').forEach(btn => {
                    btn.textContent = '显示详情';
                });
            }

            setupSearch() {
                // 添加搜索框
                const searchBox = document.createElement('input');
                searchBox.type = 'text';
                searchBox.placeholder = '🔍 搜索表格、策略或权限...';
                searchBox.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border: 2px solid #4299e1;
                    border-radius: 25px;
                    background: white;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                    z-index: 1000;
                    font-size: 14px;
                    width: 250px;
                    transition: all 0.3s ease;
                `;

                searchBox.addEventListener('focus', () => {
                    searchBox.style.boxShadow = '0 8px 25px rgba(66, 153, 225, 0.3)';
                    searchBox.style.borderColor = '#3182ce';
                });

                searchBox.addEventListener('blur', () => {
                    searchBox.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                    searchBox.style.borderColor = '#4299e1';
                });

                searchBox.addEventListener('input', (e) => this.searchMindMap(e.target.value));
                document.body.appendChild(searchBox);
            }

            searchMindMap(query) {
                const items = document.querySelectorAll('.table-item, .policy, .permission-level, .issue-item');
                items.forEach(item => {
                    const text = item.textContent.toLowerCase();
                    const matches = text.includes(query.toLowerCase());
                    item.style.opacity = matches || query === '' ? '1' : '0.3';
                    item.style.transition = 'opacity 0.3s ease';
                });

                // 高亮匹配的文本
                if (query) {
                    this.highlightText(query);
                }
            }

            highlightText(query) {
                const regex = new RegExp(`(${query})`, 'gi');
                document.querySelectorAll('.table-name, .policy-name, .permission-title, .issue-code').forEach(element => {
                    const originalText = element.textContent;
                    if (originalText.toLowerCase().includes(query.toLowerCase())) {
                        element.innerHTML = originalText.replace(regex, '<mark style="background: #ffd700; padding: 2px;">$1</mark>');
                    }
                });
            }

            exportData() {
                const data = {
                    timestamp: new Date().toISOString(),
                    tables: [],
                    policies: [],
                    permissions: [],
                    issues: []
                };

                // 收集表格数据
                document.querySelectorAll('.table-item').forEach(item => {
                    const tableName = item.querySelector('.table-name').textContent;
                    const columns = Array.from(item.querySelectorAll('.column')).map(col => col.textContent);
                    data.tables.push({ name: tableName, columns });
                });

                // 收集策略数据
                document.querySelectorAll('.policy').forEach(policy => {
                    const name = policy.querySelector('.policy-name').textContent;
                    const rule = policy.querySelector('.policy-rule').textContent;
                    data.policies.push({ name, rule });
                });

                console.log('导出的数据:', data);

                // 下载JSON文件
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'supabase-structure.json';
                a.click();
                URL.revokeObjectURL(url);

                return data;
            }
        }

        // 全局函数
        const toggleDetails = (button) => {
            const details = button.nextElementSibling;
            const isShowing = details.classList.contains('show');

            // 切换当前详情
            details.classList.toggle('show', !isShowing);
            button.textContent = isShowing ? '显示详情' : '隐藏详情';

            // 添加动画效果
            if (!isShowing) {
                details.style.animation = 'slideDown 0.3s ease';
            }
        };

        const toggleAllBranches = () => {
            const branches = document.querySelectorAll('.branch');
            branches.forEach((branch, index) => {
                setTimeout(() => {
                    branch.style.animation = 'slideDown 0.5s ease';
                    branch.style.animationDelay = `${index * 0.1}s`;
                }, index * 50);
            });

            // 显示提示信息
            const toast = document.createElement('div');
            toast.textContent = '🎉 数据库结构已展开！';
            toast.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: #48bb78;
                color: white;
                padding: 10px 20px;
                border-radius: 25px;
                z-index: 1001;
                animation: slideDown 0.3s ease;
            `;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        };

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            const mindMap = new SupabaseMindMap();

            // 添加导出按钮
            const exportBtn = document.createElement('button');
            exportBtn.textContent = '📥 导出数据';
            exportBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 25px;
                cursor: pointer;
                font-weight: bold;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 1000;
                transition: all 0.3s ease;
            `;
            exportBtn.addEventListener('click', () => mindMap.exportData());
            exportBtn.addEventListener('mouseenter', () => {
                exportBtn.style.transform = 'translateY(-2px)';
                exportBtn.style.boxShadow = '0 8px 25px rgba(0,0,0,0.3)';
            });
            exportBtn.addEventListener('mouseleave', () => {
                exportBtn.style.transform = '';
                exportBtn.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
            });
            document.body.appendChild(exportBtn);

            console.log('🎉 Supabase 脑图已加载完成！');
            console.log('💡 提示：点击根节点展开所有分支，按ESC键关闭所有详情');
        });
    </script>
</body>
</html>
