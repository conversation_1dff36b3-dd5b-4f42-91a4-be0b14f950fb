<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全面修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 全面修复测试</h1>
        
        <div class="status" id="status">
            <h3>系统状态检查：</h3>
            <div id="statusContent">检查中...</div>
        </div>
        
        <div class="test-section">
            <h3>1. Supabase客户端测试</h3>
            <button onclick="testSupabaseClient()">测试基础功能</button>
            <button onclick="testRealtimeChannel()">测试实时订阅</button>
        </div>
        
        <div class="test-section">
            <h3>2. 认证系统测试</h3>
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456" placeholder="输入密码">
            </div>
            <button onclick="testSupabaseConfigLogin()">测试supabase-config登录</button>
            <button onclick="testAuthManagerLogin()">测试auth-manager登录</button>
            <button onclick="logout()">登出</button>
        </div>
        
        <div class="test-section">
            <h3>3. 客服聊天系统测试</h3>
            <button onclick="testCustomerService()">测试客服连接</button>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- 引入所有相关脚本 -->
    <script src="supabase-simple.js"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/auth-manager.js"></script>
    <script src="js/customer-service-chat.js"></script>
    
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = type;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
            
            // 滚动到结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }

        function updateStatus() {
            const statusContent = document.getElementById('statusContent');
            
            let status = '';
            status += `window.supabase: ${typeof window.supabase !== 'undefined' ? '✅' : '❌'}\n`;
            status += `window.supabaseClient: ${typeof window.supabaseClient !== 'undefined' ? '✅' : '❌'}\n`;
            status += `supabaseClient.from: ${typeof window.supabaseClient?.from === 'function' ? '✅' : '❌'}\n`;
            status += `supabaseClient.channel: ${typeof window.supabaseClient?.channel === 'function' ? '✅' : '❌'}\n`;
            status += `loginUser函数: ${typeof window.loginUser === 'function' ? '✅' : '❌'}\n`;
            status += `AuthManager: ${typeof window.AuthManager !== 'undefined' ? '✅' : '❌'}\n`;
            status += `CustomerServiceChat: ${typeof window.CustomerServiceChat !== 'undefined' ? '✅' : '❌'}\n`;
            
            statusContent.textContent = status;
        }

        function testSupabaseClient() {
            try {
                showResult('正在测试Supabase客户端基础功能...', 'info');
                
                if (typeof window.supabaseClient?.from !== 'function') {
                    throw new Error('supabaseClient.from 不可用');
                }
                
                if (typeof window.supabaseClient?.channel !== 'function') {
                    throw new Error('supabaseClient.channel 不可用');
                }
                
                showResult('✅ Supabase客户端基础功能测试通过！', 'success');
                
            } catch (error) {
                showResult(`❌ Supabase客户端测试失败: ${error.message}`, 'error');
            }
        }

        function testRealtimeChannel() {
            try {
                showResult('正在测试实时订阅功能...', 'info');
                
                const channel = window.supabaseClient.channel('test-channel');
                
                if (typeof channel.on !== 'function') {
                    throw new Error('channel.on 方法不可用');
                }
                
                if (typeof channel.subscribe !== 'function') {
                    throw new Error('channel.subscribe 方法不可用');
                }
                
                // 测试订阅
                channel
                    .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'customer_service' }, (payload) => {
                        console.log('收到实时更新:', payload);
                    })
                    .subscribe((status, err) => {
                        if (status === 'SUBSCRIBED') {
                            showResult('✅ 实时订阅功能测试通过！', 'success');
                        } else {
                            showResult(`❌ 订阅状态异常: ${status}`, 'error');
                        }
                    });
                
            } catch (error) {
                showResult(`❌ 实时订阅测试失败: ${error.message}`, 'error');
            }
        }

        async function testSupabaseConfigLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                showResult('正在测试supabase-config登录...', 'info');
                
                const result = await loginUser(email, password);
                
                if (result.success) {
                    showResult(`✅ supabase-config登录成功！<br>用户: ${result.user.username}`, 'success');
                } else {
                    showResult(`❌ supabase-config登录失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                showResult(`❌ supabase-config登录异常: ${error.message}`, 'error');
            }
        }

        async function testAuthManagerLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                showResult('正在测试auth-manager登录...', 'info');
                
                if (typeof window.authManager === 'undefined') {
                    throw new Error('AuthManager未初始化');
                }
                
                const result = await window.authManager.loginUser(email, password);
                
                if (result.success) {
                    showResult(`✅ auth-manager登录成功！<br>用户: ${result.user.username}`, 'success');
                } else {
                    showResult(`❌ auth-manager登录失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                showResult(`❌ auth-manager登录异常: ${error.message}`, 'error');
            }
        }

        function logout() {
            try {
                // 清除所有登录状态
                localStorage.removeItem('simple_auth_user');
                localStorage.removeItem('simple_auth_login_time');
                
                if (window.logoutUser) {
                    window.logoutUser();
                }
                
                showResult('✅ 已登出', 'success');
                
            } catch (error) {
                showResult(`❌ 登出失败: ${error.message}`, 'error');
            }
        }

        function testCustomerService() {
            try {
                showResult('正在测试客服聊天系统...', 'info');
                
                if (typeof window.CustomerServiceChat === 'undefined') {
                    throw new Error('CustomerServiceChat类未定义');
                }
                
                // 创建客服聊天实例（不实际初始化DOM）
                const chatTest = {
                    checkConnection: async function() {
                        try {
                            const { data, error } = await window.supabaseClient
                                .from('customer_service')
                                .select('count')
                                .execute();
                            
                            if (error) throw error;
                            return true;
                        } catch (error) {
                            throw new Error('数据库连接失败: ' + error.message);
                        }
                    },
                    
                    setupRealtimeSubscription: function() {
                        try {
                            const channel = window.supabaseClient.channel('customer_service_channel');
                            channel.on('postgres_changes', 
                                { event: 'INSERT', schema: 'public', table: 'customer_service' }, 
                                () => {}
                            );
                            return true;
                        } catch (error) {
                            throw new Error('实时订阅设置失败: ' + error.message);
                        }
                    }
                };
                
                // 测试连接
                chatTest.checkConnection().then(() => {
                    showResult('✅ 客服数据库连接测试通过！', 'success');
                }).catch(error => {
                    showResult(`❌ 客服数据库连接失败: ${error.message}`, 'error');
                });
                
                // 测试实时订阅
                if (chatTest.setupRealtimeSubscription()) {
                    showResult('✅ 客服实时订阅设置成功！', 'success');
                }
                
            } catch (error) {
                showResult(`❌ 客服聊天系统测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateStatus();
                showResult('🔧 全面修复测试页面已加载', 'info');
                showResult('📋 修复内容：<br>• 替换了有问题的supabase-js.min.js<br>• 添加了实时订阅功能支持<br>• 修复了auth-manager认证问题<br>• 统一使用简化认证系统', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
