<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品详情 - 春盛机械</title>

    <!-- 原网站样式 -->
    <link href="pcstyle.css" rel="stylesheet" type="text/css">
    <link href="reset.css" rel="stylesheet" type="text/css">
    <link href="32_Pc_zh-CN.css" rel="stylesheet" type="text/css">

    <!-- 自定义样式 -->
    <link href="css/custom.css" rel="stylesheet" type="text/css">
    <link href="css/customer-service-chat.css" rel="stylesheet" type="text/css">

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f8f8f8;
            line-height: 1.6;
        }



        .breadcrumb {
            background: white;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }

        .breadcrumb-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            color: #666;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #666;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            color: #e74c3c;
        }

        .main-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 0 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .product-header {
            text-align: center;
            padding: 40px 20px;
            border-bottom: 1px solid #eee;
        }

        .product-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
        }

        .product-meta {
            color: #999;
            font-size: 14px;
        }

        .product-content {
            display: flex;
            padding: 40px 20px;
            gap: 40px;
        }

        .product-image {
            flex: 1;
            text-align: center;
            margin-top: 90px; /* 图片往下移动90px */
        }

        .product-image img {
            max-width: 100%;
            height: auto;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .product-info {
            flex: 1;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .info-table th,
        .info-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .info-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            width: 120px;
        }

        .info-table td {
            color: #666;
        }



        .loading {
            text-align: center;
            padding: 100px 20px;
            color: #666;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #e74c3c;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            text-align: center;
            padding: 100px 20px;
            color: #e74c3c;
        }

        @media (max-width: 768px) {
            .product-content {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header class="header-section">
        <!-- Logo区域 -->
        <div class="logo-section" style="background-color: rgb(37, 37, 37); padding: 10px 0;">
            <div class="container" style="width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center;">
                <div class="logo">
                    <img src="16792504.png" alt="安徽春晟机械有限公司" style="height: 60px;">
                </div>
                <div class="user-actions">
                    <a href="login.html" class="login-btn" style="color: white; margin-right: 15px;">登录</a>
                    <a href="register.html" class="register-btn" style="background: #be131b; color: white; padding: 5px 15px;">注册</a>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <nav class="navigation" style="background-color: #F5F5F5; border-bottom: 1px solid #ddd;">
            <div class="container" style="width: 1200px; margin: 0 auto;">
                <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0;">
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="index.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">网站首页</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="about.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">关于我们</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">企业展示</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="factory.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">工厂设备</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="products.html" style="display: block; padding: 20px; color: #be131b; text-decoration: none; transition: all 0.3s; font-weight: bold;">产品中心</a>
                    </li>
                    <li style="flex: 1; text-align: center; border-right: 1px solid #ddd;">
                        <a href="news.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">新闻中心</a>
                    </li>
                    <li style="flex: 1; text-align: center;">
                        <a href="contact.html" style="display: block; padding: 20px; color: #666; text-decoration: none; transition: all 0.3s;">联系我们</a>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- 面包屑导航 -->
    <nav class="breadcrumb">
        <div class="breadcrumb-content">
            <a href="index.html">网站首页</a> > 
            <a href="products.html">产品分类展示</a> > 
            <span>支架总成</span> > 
            <span id="current-product">产品详情</span>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-container">
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在加载产品信息...</p>
        </div>

        <div id="error" class="error" style="display: none;">
            <h3>加载失败</h3>
            <p id="error-message"></p>
            <button onclick="location.reload()" style="margin-top: 15px; padding: 10px 20px; background: #e74c3c; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
        </div>

        <div id="content" style="display: none;">
            <!-- 产品标题 -->
            <div class="product-header">
                <h1 class="product-title" id="product-title">产品名称</h1>
                <div class="product-meta">
                    <span>创建时间：</span><span id="create-time">2000-06-22 01:05</span>
                </div>
            </div>

            <!-- 产品内容 -->
            <div class="product-content">
                <div class="product-image">
                    <img id="product-img" src="" alt="产品图片">
                </div>
                <div class="product-info">
                    <table class="info-table">
                        <tr>
                            <th>产品编号：</th>
                            <td id="product-code"></td>
                        </tr>
                        <tr>
                            <th>产品名称：</th>
                            <td id="product-name"></td>
                        </tr>
                        <tr>
                            <th>适用车型：</th>
                            <td id="car-models"></td>
                        </tr>
                        <tr>
                            <th>最高承载量：</th>
                            <td id="max-load"></td>
                        </tr>
                        <tr>
                            <th>其他备注：</th>
                            <td id="other-notes"></td>
                        </tr>
                    </table>
                </div>
            </div>


        </div>
    </main>

    <script src="js/supabase-init.js"></script>
    <script>
        // 简化的权限检查函数
        let currentUserData = null;

        async function getCurrentUserData() {
            if (currentUserData) return currentUserData;

            if (!window.supabaseClient) return null;

            try {
                const { data: { user } } = await window.supabaseClient.auth.getUser();
                if (!user) return null;

                const { data: userData } = await window.supabaseClient
                    .from('users')
                    .select('*')
                    .eq('email', user.email)
                    .single();

                currentUserData = userData;
                return userData;
            } catch (error) {
                console.error('获取用户数据失败:', error);
                return null;
            }
        }

        async function canDownload() {
            const userData = await getCurrentUserData();
            if (!userData) return false;

            // 特许用户及以上可以下载PDF
            return ['privileged', 'admin'].includes(userData.user_type);
        }

        async function canViewDetails() {
            const userData = await getCurrentUserData();
            if (!userData) return false;

            // 高级用户及以上可以查看详情
            return ['premium', 'privileged', 'admin'].includes(userData.user_type);
        }
    </script>
    <script>
        // 🔍 DEBUG: 产品详情页面调试日志
        console.log('🔍 [PRODUCT-DETAIL] 页面开始加载');
        console.log('🔍 [PRODUCT-DETAIL] URL:', window.location.href);
        
        // 从URL参数获取产品ID
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');
        console.log('🔍 [PRODUCT-DETAIL] 产品ID:', productId);

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔍 [PRODUCT-DETAIL] DOM加载完成，开始初始化');
            
            // 检查Supabase客户端状态
            console.log('🔍 [PRODUCT-DETAIL] 检查Supabase客户端:');
            console.log('  - window.supabase存在:', typeof window.supabase !== 'undefined');
            console.log('  - 全局supabase存在:', typeof supabase !== 'undefined');
            console.log('  - authManager存在:', typeof window.authManager !== 'undefined');
            
            // 检查认证管理器状态
            if (typeof window.authManager !== 'undefined') {
                console.log('🔍 [PRODUCT-DETAIL] AuthManager状态:');
                console.log('  - 当前用户:', window.authManager.getCurrentUser());
                console.log('  - 用户类型:', window.authManager.getUserType());
                console.log('  - 是否已登录:', window.authManager.isLoggedIn());
                console.log('  - 可查看详情:', window.authManager.canViewDetails());
            }
            
            // 检查全局变量状态
            console.log('🔍 [PRODUCT-DETAIL] 全局变量状态:');
            console.log('  - currentUser:', window.currentUser);
            console.log('  - currentUserType:', window.currentUserType);
            console.log('  - canViewDetails():', typeof canViewDetails === 'function' ? canViewDetails() : 'function not found');

            // 等待Supabase初始化并检查用户权限
            console.log('🔍 [PRODUCT-DETAIL] 等待Supabase初始化...');

            let userCanView = false;
            let waitCount = 0;
            const maxWait = 10; // 最多等待5秒

            while (waitCount < maxWait) {
                await new Promise(resolve => setTimeout(resolve, 500));
                waitCount++;

                console.log(`🔍 [PRODUCT-DETAIL] 等待第${waitCount}次 (${waitCount * 500}ms)`);

                if (window.supabaseClient) {
                    console.log('🔍 [PRODUCT-DETAIL] Supabase客户端已就绪，检查用户权限');

                    try {
                        const { data: { user } } = await window.supabaseClient.auth.getUser();

                        if (user) {
                            console.log('🔍 [PRODUCT-DETAIL] 用户已登录:', user.email);

                            // 查询用户权限
                            const { data: userData } = await window.supabaseClient
                                .from('users')
                                .select('user_type')
                                .eq('email', user.email)
                                .single();

                            if (userData) {
                                const userType = userData.user_type;
                                console.log('🔍 [PRODUCT-DETAIL] 用户权限:', userType);

                                // 检查权限：高级用户及以上可以查看详情
                                userCanView = ['premium', 'privileged', 'admin'].includes(userType);
                                console.log('🔍 [PRODUCT-DETAIL] 可查看详情:', userCanView);
                                break;
                            }
                        } else {
                            console.log('🔍 [PRODUCT-DETAIL] 用户未登录，权限不足');
                            userCanView = false;
                            break;
                        }
                    } catch (error) {
                        console.error('🔍 [PRODUCT-DETAIL] 权限检查失败:', error);
                    }
                }

                if (waitCount >= maxWait) {
                    console.warn('🔍 [PRODUCT-DETAIL] 等待超时');
                }
            }

            // 检查用户权限
            if (!userCanView) {
                console.log('🔍 [PRODUCT-DETAIL] 用户权限不足');
                showError('您的权限不足，无法查看产品详情。请注册登录后重试。');
                return;
            }

            console.log('🔍 [PRODUCT-DETAIL] 权限检查通过，开始加载产品');
            if (!productId) {
                showError('缺少产品ID参数');
            } else {
                loadProductDetail(productId);
            }
        });

        async function loadProductDetail(productId) {
            console.log('🔍 [PRODUCT-DETAIL] 开始加载产品详情，ID:', productId);
            
            try {
                // 检查Supabase客户端
                if (typeof supabase === 'undefined') {
                    throw new Error('Supabase客户端未初始化');
                }
                
                console.log('🔍 [PRODUCT-DETAIL] 发送数据库查询...');
                
                // 获取产品信息
                const { data: product, error } = await supabase
                    .from('products')
                    .select('*')
                    .eq('id', productId)
                    .single();

                console.log('🔍 [PRODUCT-DETAIL] 数据库查询结果:');
                console.log('  - 错误:', error);
                console.log('  - 产品数据:', product);

                if (error) {
                    console.error('🔍 [PRODUCT-DETAIL] 数据库查询错误:', error);
                    throw error;
                }
                if (!product) {
                    console.error('🔍 [PRODUCT-DETAIL] 产品不存在');
                    throw new Error('产品不存在');
                }

                console.log('🔍 [PRODUCT-DETAIL] 产品加载成功，开始显示');
                // 显示产品信息
                await displayProductDetail(product);

                // 隐藏加载动画，显示内容
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
                
                console.log('🔍 [PRODUCT-DETAIL] 产品详情显示完成');

            } catch (error) {
                console.error('🔍 [PRODUCT-DETAIL] 加载失败:', error);
                showError(error.message);
            }
        }

        async function displayProductDetail(product) {
            // 设置产品标题
            document.getElementById('product-title').textContent = product.product_name;
            document.getElementById('current-product').textContent = product.product_name;
            
            // 设置产品图片
            const productImg = document.getElementById('product-img');
            if (product.product_image) {
                productImg.src = product.product_image;
            } else {
                // 使用默认图片
                const productImages = [
                    '16029111.png', '16029112.png', '16029113.png', '16029114.png',
                    '16029115.png', '16029116.png', '16029117.png', '16029118.png',
                    '16029137.png', '16029138.png', '16029139.png', '16029140.png'
                ];
                const imageIndex = parseInt(product.id) % productImages.length;
                productImg.src = productImages[imageIndex];
            }
            
            // 设置产品信息
            document.getElementById('product-code').textContent = product.data_id;
            document.getElementById('product-name').textContent = product.product_name;
            document.getElementById('car-models').textContent = '待补充';
            document.getElementById('max-load').textContent = '待补充';
            document.getElementById('other-notes').textContent = product.remarks || '无';
            
            // 设置创建时间
            if (product.created_at) {
                const date = new Date(product.created_at);
                document.getElementById('create-time').textContent = date.toLocaleString('zh-CN');
            }

            // 检查PDF下载权限
            await checkPDFDownloadPermission(product);
        }

        async function checkPDFDownloadPermission(product) {
            // 如果用户有下载权限且产品有PDF附件
            const userCanDownload = await canDownload();
            if (userCanDownload && product.attachment_path) {
                // 检查是否是有效的PDF
                const isValidPDF = product.attachment_path.includes('supabase.co') &&
                                 product.attachment_path.endsWith('.pdf');

                if (isValidPDF) {
                    // 在产品信息表格后添加PDF下载区域
                    const productInfo = document.querySelector('.product-info');
                    const downloadSection = document.createElement('div');
                    downloadSection.className = 'pdf-download-section';
                    downloadSection.style.cssText = `
                        margin-top: 30px;
                        padding: 20px;
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    `;

                    const downloadFileName = `${product.data_id}_技术文档.pdf`;
                    downloadSection.innerHTML = `
                        <h3 style="color: #be131b; margin-bottom: 15px;">📄 技术文档</h3>
                        <div style="display: flex; gap: 15px; align-items: center;">
                            <button onclick="window.open('${product.attachment_path}', '_blank')"
                                    style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                🔍 预览PDF
                            </button>
                            <button onclick="downloadPDF('${product.attachment_path}', '${downloadFileName}')"
                                    style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                📥 下载PDF
                            </button>
                            <span style="color: #666; font-size: 14px;">文件名：${downloadFileName}</span>
                        </div>
                    `;

                    productInfo.appendChild(downloadSection);
                }
            } else if (!userCanDownload) {
                // 显示权限提示
                const productInfo = document.querySelector('.product-info');
                const permissionNotice = document.createElement('div');
                permissionNotice.style.cssText = `
                    margin-top: 30px;
                    padding: 20px;
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    color: #856404;
                    text-align: center;
                `;
                permissionNotice.innerHTML = `
                    <h4>🔒 权限提示</h4>
                    <p>特许用户可下载PDF技术文档，请联系管理员升级账户权限。</p>
                `;
                productInfo.appendChild(permissionNotice);
            }
        }

        function downloadPDF(pdfUrl, fileName) {
            try {
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = fileName;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示下载成功提示
                alert(`PDF文档 "${fileName}" 下载已开始`);
            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败，请稍后重试');
            }
        }

        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }
    </script>
</body>
</html>
