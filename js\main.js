// 主要JavaScript功能

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// 初始化页面
async function initializePage() {
    // 如果是首页，加载产品
    if (document.getElementById('products-grid')) {
        await loadHomeProducts();
    }
    
    // 如果是产品页面，加载所有产品
    if (document.getElementById('all-products-grid')) {
        await loadAllProducts();
    }
    
    // 如果是车型页面，加载车型数据
    if (document.getElementById('car-models-container')) {
        await loadCarModels();
    }
    
    // 初始化模态框
    initializeModal();
    
    // 初始化导航高亮
    highlightCurrentNav();
}

// 加载首页产品（限制数量）
async function loadHomeProducts() {
    const container = document.getElementById('products-grid');
    if (!container) return;
    
    // 显示加载动画
    container.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
    
    try {
        const products = await getProducts(6); // 首页只显示6个产品
        displayProducts(products, container);
    } catch (error) {
        console.error('加载产品失败:', error);
        container.innerHTML = '<p style="text-align: center; color: #666;">加载产品失败，请稍后重试</p>';
    }
}

// 加载所有产品
async function loadAllProducts() {
    const container = document.getElementById('all-products-grid');
    if (!container) return;
    
    // 显示加载动画
    container.innerHTML = '<div class="loading"><div class="spinner"></div></div>';
    
    try {
        const products = await getProducts(); // 加载所有产品
        displayProducts(products, container);
    } catch (error) {
        console.error('加载产品失败:', error);
        container.innerHTML = '<p style="text-align: center; color: #666;">加载产品失败，请稍后重试</p>';
    }
}

// 显示产品列表 - 简化版本，使用链接跳转
function displayProducts(products, container) {
    if (!products || products.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666;">暂无产品数据</p>';
        return;
    }

    const productsHTML = products.map(product => createProductCard(product)).join('');
    container.innerHTML = productsHTML;
}

// 创建产品卡片HTML - 重构版本，直接包含下载功能
function createProductCard(product) {
    // 为不同产品分配不同的示例图片
    const productImages = [
        '16029111.png', '16029112.png', '16029113.png', '16029114.png',
        '16029115.png', '16029116.png', '16029117.png', '16029118.png',
        '16029137.png', '16029138.png', '16029139.png', '16029140.png'
    ];

    const imageIndex = parseInt(product.id) % productImages.length;
    const defaultImage = productImages[imageIndex];

    // 检查是否有有效的PDF
    const hasValidPDF = product.attachment_path &&
                       product.attachment_path.includes('supabase.co') &&
                       product.attachment_path.endsWith('.pdf');

    // 生成查看文档按钮
    let documentButton = '';
    if (canDownload() && hasValidPDF) {
        documentButton = `
            <a href="pdf-viewer.html?id=${product.id}"
               style="display: block; width: 100%; margin-top: 10px; padding: 10px 12px; background: #007bff; color: white; text-decoration: none; text-align: center; border-radius: 5px; font-weight: bold;">
                📄 查看技术文档
            </a>
        `;
    } else if (canDownload() && !hasValidPDF) {
        documentButton = `
            <div style="width: 100%; margin-top: 10px; padding: 8px 12px; background: #f8f9fa; color: #6c757d; border: 1px dashed #dee2e6; border-radius: 4px; text-align: center; font-size: 12px;">
                📄 暂无技术文档
            </div>
        `;
    } else {
        documentButton = `
            <div style="width: 100%; margin-top: 10px; padding: 8px 12px; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; border-radius: 4px; text-align: center; font-size: 12px;">
                🔒 特许用户可下载PDF文档
            </div>
        `;
    }

    return `
        <div class="product-card" data-product-id="${product.id}" onclick="window.location.href='product-detail.html?id=${product.id}'" style="cursor: pointer;">
            <div class="product-image" style="width: 100%; height: 200px; background: #f5f5f5; display: flex; align-items: center; justify-content: center; border: 1px solid #e0e0e0;">
                ${product.product_image ?
                    `<img src="${product.product_image}" alt="${product.product_name}" style="max-width: 100%; max-height: 100%; object-fit: contain;">` :
                    `<img src="${defaultImage}" alt="${product.product_name}" style="max-width: 100%; max-height: 100%; object-fit: contain;">`
                }
            </div>
            <div class="product-name" style="text-align: center; padding: 10px; background: white; border: 1px solid #e0e0e0; border-top: none; font-size: 14px; color: #333;">
                ${product.product_name}
            </div>
        </div>
    `;
}

// 获取权限提示
function getPermissionNotice() {
    if (currentUserType === USER_TYPES.ADMIN) {
        return '<div class="permission-notice admin">管理员：可查看详细信息并下载所有文档</div>';
    } else if (currentUserType === USER_TYPES.PRIVILEGED) {
        return '<div class="permission-notice privileged">特许用户：可查看详细工艺信息并下载PDF文档</div>';
    } else if (currentUserType === USER_TYPES.PREMIUM) {
        return '<div class="permission-notice premium">高级用户：可查看详细工艺信息</div>';
    } else {
        return '<div class="permission-notice guest">游客：<a href="login.html">注册登录</a>后可查看更多信息</div>';
    }
}

// 显示产品详情模态框
async function showProductModal(productId) {
    console.log('显示产品详情模态框:', productId);

    const modal = document.getElementById('product-modal');
    if (!modal) {
        console.error('找不到产品模态框元素');
        return;
    }

    try {
        console.log('开始获取产品数据...');
        const product = await getProductById(productId);
        console.log('获取到产品数据:', product);
        if (!product) {
            alert('产品信息加载失败');
            return;
        }

        // 填充模态框内容
        fillProductModal(product);

        // 显示模态框
        modal.style.display = 'block';
    } catch (error) {
        console.error('加载产品详情失败:', error);
        alert('产品详情加载失败，请稍后重试');
    }
}

// 在新标签页中打开PDF全屏预览
function openPDFInNewTab(pdfUrl) {
    if (!pdfUrl) {
        alert('PDF文档不存在');
        return;
    }

    // 在新标签页中打开PDF
    window.open(pdfUrl, '_blank');
}

// PDF下载功能 - 增强错误处理
async function downloadPDFWithName(productId, pdfUrl, fileName) {
    console.log('开始下载PDF:', { productId, pdfUrl, fileName });

    // 检查用户权限
    if (!canDownload()) {
        alert('您的权限不足，无法下载文档。请联系管理员升级账户。');
        return;
    }

    if (!currentUser) {
        alert('请先登录后再下载文档');
        return;
    }

    try {
        // 记录下载
        await recordDownload(productId, 'pdf');

        // 先检查文件是否可访问
        console.log('检查文件可访问性...');
        const checkResponse = await fetch(pdfUrl, { method: 'HEAD' });

        if (!checkResponse.ok) {
            throw new Error(`文件不可访问 (HTTP ${checkResponse.status})`);
        }

        console.log('文件检查通过，开始下载...');

        // 方法1: 直接链接下载
        const link = document.createElement('a');
        link.href = pdfUrl;
        link.download = fileName;
        link.target = '_blank';
        link.style.display = 'none';

        document.body.appendChild(link);

        // 添加事件监听器来检测下载状态
        link.addEventListener('click', function() {
            console.log('下载链接已点击');
            setTimeout(() => {
                document.body.removeChild(link);
            }, 1000);
        });

        link.click();

        showNotification(`文档 "${fileName}" 下载已开始`, 'success');
        console.log('下载命令已发送');

    } catch (error) {
        console.error('下载失败:', error);

        // 如果直接下载失败，尝试在新窗口打开
        console.log('尝试在新窗口打开PDF...');
        try {
            window.open(pdfUrl, '_blank');
            showNotification('PDF已在新窗口打开，请手动保存', 'info');
        } catch (openError) {
            console.error('新窗口打开也失败:', openError);
            alert(`下载失败: ${error.message}\n\n请尝试右键点击链接选择"另存为"`);
        }
    }
}



// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;

    // 根据类型设置背景色
    switch(type) {
        case 'success':
            notification.style.backgroundColor = '#4caf50';
            break;
        case 'error':
            notification.style.backgroundColor = '#f44336';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ff9800';
            break;
        default:
            notification.style.backgroundColor = '#2196f3';
    }

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 填充产品模态框内容
async function fillProductModal(product) {
    const modal = document.getElementById('product-modal');
    
    // 基本信息
    modal.querySelector('.modal-title').textContent = product.product_name;
    
    // 产品图片
    const imageContainer = modal.querySelector('.product-detail-image');
    const productImages = [
        '16029111.png', '16029112.png', '16029113.png', '16029114.png',
        '16029115.png', '16029116.png', '16029117.png', '16029118.png',
        '16029137.png', '16029138.png', '16029139.png', '16029140.png'
    ];
    const imageIndex = parseInt(product.id) % productImages.length;
    const defaultImage = productImages[imageIndex];

    imageContainer.innerHTML = product.product_image ?
        `<img src="${product.product_image}" alt="${product.product_name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">` :
        `<img src="${defaultImage}" alt="${product.product_name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
    
    // 基本详情
    const detailsContainer = modal.querySelector('.product-details-info');
    let detailsHTML = `
        <div class="detail-item">
            <div class="detail-label">数据ID</div>
            <div class="detail-value">${product.data_id}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">存货编码</div>
            <div class="detail-value">${product.stock_code}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">产品类别</div>
            <div class="detail-value">${product.product_category}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">规格</div>
            <div class="detail-value">${product.specifications}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">材质</div>
            <div class="detail-value">${product.material}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">料厚</div>
            <div class="detail-value">${product.thickness}</div>
        </div>
    `;
    
    // 高级用户和特许用户可以看到详细工艺信息
    if (canViewDetails()) {
        detailsHTML += `
            <div class="detail-section">
                <h4>详细工艺信息</h4>
                <div class="detail-item">
                    <div class="detail-label">外形编码</div>
                    <div class="detail-value">${product.shape_code || '-'}</div>
                </div>
        `;

        // 主要工艺信息
        if (product.main_process_1) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">主要工艺1</div>
                    <div class="detail-value">${product.main_process_1}</div>
                </div>
            `;
        }

        if (product.main_process_2) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">主要工艺2</div>
                    <div class="detail-value">${product.main_process_2}</div>
                </div>
            `;
        }

        if (product.main_process_3) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">主要工艺3</div>
                    <div class="detail-value">${product.main_process_3}</div>
                </div>
            `;
        }

        if (product.main_process_4) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">主要工艺4</div>
                    <div class="detail-value">${product.main_process_4}</div>
                </div>
            `;
        }

        // 可变工艺信息
        if (product.variable_process_1) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">可变加工艺1</div>
                    <div class="detail-value">${product.variable_process_1}</div>
                </div>
            `;
        }

        if (product.variable_process_2) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">可变加工艺2</div>
                    <div class="detail-value">${product.variable_process_2}</div>
                </div>
            `;
        }

        if (product.variable_process_3) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">可变加工艺3</div>
                    <div class="detail-value">${product.variable_process_3}</div>
                </div>
            `;
        }

        // 工艺数量
        if (product.process_count) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">工艺数量</div>
                    <div class="detail-value">${product.process_count}</div>
                </div>
            `;
        }

        // 详细描述（仅特许用户可见）
        if (currentUserType === USER_TYPES.PRIVILEGED && product.detail_description) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">详细描述</div>
                    <div class="detail-value">${product.detail_description}</div>
                </div>
            `;
        }

        // 备注信息
        if (product.remarks) {
            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">备注</div>
                    <div class="detail-value">${product.remarks}</div>
                </div>
            `;
        }

        detailsHTML += '</div>'; // 结束 detail-section
    } else {
        detailsHTML += `
            <div class="detail-section">
                <div class="permission-notice">
                    <strong>升级账户查看更多信息</strong><br>
                    • 高级用户：可查看详细工艺信息<br>
                    • 特许用户：可查看完整技术资料并下载PDF文档<br>
                    • 管理员：完全管理权限
                </div>
            </div>
        `;
    }
    
    // 调试信息
    console.log('PDF显示检查:', {
        canDownload: canDownload(),
        hasAttachment: !!product.attachment_path,
        attachmentPath: product.attachment_path,
        currentUser: currentUser,
        currentUserType: currentUserType
    });

    // 特许用户可以预览和下载文档
    if (canDownload() && product.attachment_path) {
        // 只处理有效的Supabase PDF
        const isValidPDF = product.attachment_path.includes('supabase.co') && product.attachment_path.endsWith('.pdf');

        console.log('PDF格式检查:', {
            isValidPDF,
            attachmentPath: product.attachment_path
        });

        if (isValidPDF) {
            const downloadFileName = `${product.data_id}_技术文档.pdf`;

            detailsHTML += `
                <div class="detail-item download-section">
                    <div class="detail-label">技术文档</div>
                    <div class="detail-value">
                        <div class="pdf-preview-container">
                            <iframe src="${product.attachment_path}"
                                    title="PDF预览">
                            </iframe>
                        </div>
                        <div class="pdf-actions">
                            <button class="btn btn-primary" onclick="openPDFInNewTab('${product.attachment_path}')">
                                🔍 全屏预览
                            </button>
                            <button class="btn btn-download" onclick="console.log('下载按钮被点击', '${product.id}', '${product.attachment_path}', '${downloadFileName}'); downloadPDFWithName('${product.id}', '${product.attachment_path}', '${downloadFileName}')">
                                📄 下载 ${downloadFileName}
                            </button>
                        </div>
                    </div>
                </div>
            `;
        } else {
            // 旧格式提示
            detailsHTML += `
                <div class="detail-item download-section">
                    <div class="detail-label">技术文档</div>
                    <div class="detail-value">
                        <div class="no-document-notice">
                            📄 技术文档暂不可用，请联系管理员
                        </div>
                    </div>
                </div>
            `;
        }
    } else {
        // 权限不足或无PDF时的调试信息
        console.log('PDF不显示的原因:', {
            canDownload: canDownload(),
            hasAttachment: !!product.attachment_path,
            reason: !canDownload() ? '权限不足' : '无PDF附件'
        });

        if (!canDownload()) {
            detailsHTML += `
                <div class="detail-item download-section">
                    <div class="detail-label">技术文档</div>
                    <div class="detail-value">
                        <div class="permission-notice">特许用户可预览和下载PDF技术文档</div>
                    </div>
                </div>
            `;
        }
    }

    // 有权限但无PDF的情况
    if (canDownload() && !product.attachment_path) {
        detailsHTML += `
            <div class="detail-item download-section">
                <div class="detail-label">技术文档</div>
                <div class="detail-value">
                    <div class="no-document-notice" style="color: #666; font-style: italic;">
                        暂无技术文档
                    </div>
                </div>
            </div>
        `;
    } else if (!canDownload()) {
        detailsHTML += `
            <div class="detail-item download-section">
                <div class="detail-label">技术文档</div>
                <div class="detail-value">
                    <div class="permission-notice">特许用户可预览和下载PDF技术文档</div>
                </div>
            </div>
        `;
    }
    
    detailsContainer.innerHTML = detailsHTML;

    // 加载相似产品推荐
    await loadSimilarProducts(product);
}

// 加载相似产品推荐
async function loadSimilarProducts(currentProduct) {
    try {
        // 使用智能搜索找到相似产品
        if (typeof SmartSearch !== 'undefined' && smartSearch) {
            const searchQuery = `${currentProduct.specifications || ''} ${currentProduct.material || ''}`.trim();
            if (searchQuery) {
                const results = smartSearch.search(searchQuery, {
                    includeApproximate: true,
                    tolerance: 5
                });

                // 过滤掉当前产品，获取前5个相似产品
                const similarProducts = [...results.exact, ...results.approximate]
                    .filter(p => p.id !== currentProduct.id)
                    .slice(0, 5);

                if (similarProducts.length > 0) {
                    displaySimilarProducts(similarProducts);
                }
            }
        }
    } catch (error) {
        console.error('加载相似产品失败:', error);
    }
}

// 显示相似产品
function displaySimilarProducts(products) {
    const detailsContainer = document.getElementById('product-details');
    const similarSection = document.createElement('div');
    similarSection.className = 'similar-products-section';

    let html = `
        <div class="detail-section">
            <h4>相似产品推荐</h4>
            <div class="similar-products-grid">
    `;

    products.forEach(product => {
        html += `
            <div class="similar-product-item" onclick="showProductModal(${product.id})">
                <div class="similar-product-image">
                    <img src="${product.image_path || 'images/default-product.jpg'}"
                         alt="${product.product_name}"
                         onerror="this.src='images/default-product.jpg'">
                </div>
                <div class="similar-product-info">
                    <div class="similar-product-name">${product.product_name}</div>
                    <div class="similar-product-spec">${product.specifications || '-'}</div>
                    <div class="similar-product-id">${product.data_id}</div>
                </div>
            </div>
        `;
    });

    html += `
            </div>
        </div>
    `;

    similarSection.innerHTML = html;
    detailsContainer.appendChild(similarSection);
}

// 下载附件
async function downloadAttachment(productId, attachmentPath) {
    if (!canDownload()) {
        alert('只有特许用户可以下载技术文档。请联系管理员升级账户权限。');
        return;
    }

    try {
        // 记录下载
        await recordDownload(productId, 'pdf');

        // 创建下载链接
        const link = document.createElement('a');
        link.href = attachmentPath;
        link.download = attachmentPath.split('/').pop(); // 获取文件名
        link.target = '_blank';

        // 检查文件是否存在
        const response = await fetch(attachmentPath, { method: 'HEAD' });

        if (response.ok) {
            // 文件存在，开始下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 显示下载成功提示
            showDownloadSuccess(attachmentPath);
        } else {
            // 文件不存在，生成示例PDF
            generateSamplePDF(productId, attachmentPath);
        }
    } catch (error) {
        console.error('下载失败:', error);

        // 如果网络请求失败，尝试生成示例PDF
        generateSamplePDF(productId, attachmentPath);
    }
}

// 生成示例PDF（实际项目中应该是真实的PDF文件）
function generateSamplePDF(productId, attachmentPath) {
    // 获取产品信息
    getProductById(productId).then(product => {
        if (!product) {
            alert('产品信息获取失败');
            return;
        }

        // 创建PDF内容（这里使用简单的HTML转PDF方案）
        const pdfContent = generatePDFContent(product);

        // 创建Blob并下载
        const blob = new Blob([pdfContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `${product.data_id}_${product.product_name}_技术文档.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);

        showDownloadSuccess(attachmentPath);
    });
}

// 生成PDF内容
function generatePDFContent(product) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>${product.product_name} - 技术文档</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #be131b; padding-bottom: 20px; margin-bottom: 30px; }
        .company-name { color: #be131b; font-size: 24px; font-weight: bold; }
        .doc-title { font-size: 20px; margin: 10px 0; }
        .section { margin: 20px 0; }
        .section-title { background: #f8f9fa; padding: 10px; border-left: 4px solid #be131b; font-weight: bold; }
        .info-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .info-table th { background: #f8f9fa; font-weight: bold; }
        .footer { margin-top: 50px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">安徽春晟机械有限公司</div>
        <div class="doc-title">${product.product_name} 技术文档</div>
        <div>文档编号: ${product.data_id} | 生成时间: ${new Date().toLocaleString()}</div>
    </div>

    <div class="section">
        <div class="section-title">基本信息</div>
        <table class="info-table">
            <tr><th>数据ID</th><td>${product.data_id}</td></tr>
            <tr><th>存货编码</th><td>${product.stock_code}</td></tr>
            <tr><th>产品名称</th><td>${product.product_name}</td></tr>
            <tr><th>产品类别</th><td>${product.product_category}</td></tr>
            <tr><th>产品规格</th><td>${product.specifications}</td></tr>
            <tr><th>材质</th><td>${product.material}</td></tr>
            <tr><th>料厚</th><td>${product.thickness}</td></tr>
            <tr><th>外形编码</th><td>${product.shape_code || '-'}</td></tr>
        </table>
    </div>

    <div class="section">
        <div class="section-title">工艺信息</div>
        <table class="info-table">
            <tr><th>主要工艺1</th><td>${product.main_process_1 || '-'}</td></tr>
            <tr><th>主要工艺2</th><td>${product.main_process_2 || '-'}</td></tr>
            <tr><th>主要工艺3</th><td>${product.main_process_3 || '-'}</td></tr>
            <tr><th>主要工艺4</th><td>${product.main_process_4 || '-'}</td></tr>
            <tr><th>工序数</th><td>${product.process_count || '-'}</td></tr>
            <tr><th>可变加工艺1</th><td>${product.variable_process_1 || '-'}</td></tr>
            <tr><th>可变加工艺2</th><td>${product.variable_process_2 || '-'}</td></tr>
            <tr><th>可变加工艺3</th><td>${product.variable_process_3 || '-'}</td></tr>
        </table>
    </div>

    ${product.remarks ? `
    <div class="section">
        <div class="section-title">备注说明</div>
        <p>${product.remarks}</p>
    </div>
    ` : ''}

    <div class="footer">
        <p>安徽春晟机械有限公司</p>
        <p>地址：安徽省广德经济开发区 | 专业从事减震器冲压件设计与生产</p>
        <p>本文档为机密技术资料，仅供授权用户使用</p>
    </div>
</body>
</html>
    `;
}

// 显示下载成功提示
function showDownloadSuccess(filePath) {
    const fileName = filePath.split('/').pop();

    // 创建成功提示
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;
    successDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <span>✓</span>
            <div>
                <div style="font-weight: bold;">下载成功</div>
                <div style="font-size: 12px; opacity: 0.9;">${fileName}</div>
            </div>
        </div>
    `;

    document.body.appendChild(successDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.parentNode.removeChild(successDiv);
        }
    }, 3000);
}

// 初始化模态框
function initializeModal() {
    // 创建产品详情模态框
    if (!document.getElementById('product-modal')) {
        const modalHTML = `
            <div id="product-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 class="modal-title">产品详情</h2>
                        <span class="close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <div class="product-detail-image">产品图片</div>
                        <div class="product-details-info"></div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }
    
    // 添加关闭事件
    const modal = document.getElementById('product-modal');
    const closeBtn = modal.querySelector('.close');
    
    closeBtn.onclick = function() {
        modal.style.display = 'none';
    };
    
    window.onclick = function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };
}

// 高亮当前导航
function highlightCurrentNav() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPage) {
            link.style.backgroundColor = '#be131b';
            link.style.color = 'white';
        }
    });
}

// 加载车型数据
async function loadCarModels() {
    const container = document.getElementById('car-models-container');
    if (!container) return;
    
    try {
        const carModels = await getCarModels();
        displayCarModels(carModels, container);
    } catch (error) {
        console.error('加载车型失败:', error);
        container.innerHTML = '<p style="text-align: center; color: #666;">加载车型失败，请稍后重试</p>';
    }
}

// 显示车型列表
function displayCarModels(carModels, container) {
    if (!carModels || carModels.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #666;">暂无车型数据</p>';
        return;
    }
    
    // 按首字母分组
    const groupedModels = {};
    carModels.forEach(model => {
        if (!groupedModels[model.first_letter]) {
            groupedModels[model.first_letter] = [];
        }
        groupedModels[model.first_letter].push(model);
    });
    
    let html = '';
    Object.keys(groupedModels).sort().forEach(letter => {
        html += `
            <div class="letter-group">
                <h3 style="color: #be131b; border-bottom: 2px solid #be131b; padding-bottom: 10px;">${letter}</h3>
                <div class="models-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-bottom: 40px;">
        `;
        
        groupedModels[letter].forEach(model => {
            html += `
                <div class="model-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h4 style="color: #333; margin-bottom: 10px;">${model.brand_name}</h4>
                    <p style="color: #666; margin-bottom: 10px;">${model.model_name}</p>
                    ${model.product_id ? `<p style="color: #be131b; font-size: 12px;">适配产品: ${model.product_id}</p>` : ''}
                </div>
            `;
        });
        
        html += '</div></div>';
    });
    
    container.innerHTML = html;
}
