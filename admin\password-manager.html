<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码管理工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .user-list {
            margin: 20px 0;
        }
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
            background-color: #f8f9fa;
        }
        .user-info {
            flex: 1;
        }
        .user-actions {
            display: flex;
            gap: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 10px;
            width: 400px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="password"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 密码管理工具</h1>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button onclick="loadUsers()" class="btn-success">刷新用户列表</button>
            <button onclick="showBatchReset()">批量重置密码</button>
        </div>
        
        <div id="message" class="message"></div>
        
        <div class="user-list" id="userList">
            <p>点击"刷新用户列表"加载用户数据</p>
        </div>
    </div>

    <!-- 密码重置模态框 -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <h3>重置用户密码</h3>
            <div class="form-group">
                <label>用户:</label>
                <span id="modalUserInfo"></span>
            </div>
            <div class="form-group">
                <label for="newPassword">新密码:</label>
                <input type="password" id="newPassword" placeholder="输入新密码">
            </div>
            <div class="form-group">
                <label for="confirmPassword">确认密码:</label>
                <input type="password" id="confirmPassword" placeholder="再次输入新密码">
            </div>
            <div style="text-align: right;">
                <button onclick="closeModal()">取消</button>
                <button onclick="confirmPasswordReset()" class="btn-success">确认重置</button>
            </div>
        </div>
    </div>

    <!-- 引入Supabase -->
    <script src="../supabase-simple.js"></script>
    <script src="../js/supabase-config.js"></script>
    
    <script>
        let currentResetUser = null;

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        function hashPassword(password) {
            return btoa(password + 'chunsheng_salt');
        }

        async function loadUsers() {
            try {
                showMessage('正在加载用户列表...', 'info');

                const { data: users, error } = await supabase
                    .from('users')
                    .select('*')
                    .order('created_at', { ascending: false });

                if (error) {
                    throw error;
                }

                displayUsers(users);
                showMessage(`✅ 成功加载 ${users.length} 个用户`, 'success');

            } catch (error) {
                console.error('加载用户失败:', error);
                showMessage('❌ 加载用户失败: ' + error.message, 'error');
            }
        }

        function displayUsers(users) {
            const userListDiv = document.getElementById('userList');
            
            if (!users || users.length === 0) {
                userListDiv.innerHTML = '<p>没有找到用户数据</p>';
                return;
            }

            let html = '';
            users.forEach(user => {
                const userTypeDisplay = {
                    'guest': '游客',
                    'premium': '高级用户',
                    'privileged': '特许用户',
                    'admin': '管理员'
                }[user.user_type] || user.user_type;

                const statusDisplay = user.is_active ? '✅ 活跃' : '❌ 禁用';
                const hasPassword = user.password_hash ? '✅ 已设置' : '❌ 未设置';

                html += `
                    <div class="user-item">
                        <div class="user-info">
                            <strong>${user.username}</strong> (${user.email})<br>
                            <small>权限: ${userTypeDisplay} | 状态: ${statusDisplay} | 密码: ${hasPassword}</small><br>
                            <small>公司: ${user.company_name || '未填写'} | 电话: ${user.phone || '未填写'}</small>
                        </div>
                        <div class="user-actions">
                            <button onclick="resetUserPassword('${user.id}', '${user.username}', '${user.email}')">
                                重置密码
                            </button>
                            <button onclick="toggleUserStatus('${user.id}', ${user.is_active})" 
                                    class="${user.is_active ? 'btn-danger' : 'btn-success'}">
                                ${user.is_active ? '禁用' : '启用'}
                            </button>
                        </div>
                    </div>
                `;
            });

            userListDiv.innerHTML = html;
        }

        function resetUserPassword(userId, username, email) {
            currentResetUser = { id: userId, username, email };
            document.getElementById('modalUserInfo').textContent = `${username} (${email})`;
            document.getElementById('newPassword').value = '';
            document.getElementById('confirmPassword').value = '';
            document.getElementById('passwordModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('passwordModal').style.display = 'none';
            currentResetUser = null;
        }

        async function confirmPasswordReset() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!newPassword) {
                alert('请输入新密码');
                return;
            }

            if (newPassword.length < 6) {
                alert('密码长度不能少于6位');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('两次输入的密码不一致');
                return;
            }

            try {
                showMessage('正在重置密码...', 'info');

                const hashedPassword = hashPassword(newPassword);

                const { error } = await supabase
                    .from('users')
                    .update({ password_hash: hashedPassword })
                    .eq('id', currentResetUser.id);

                if (error) {
                    throw error;
                }

                showMessage(`✅ ${currentResetUser.username} 的密码重置成功！新密码: ${newPassword}`, 'success');
                closeModal();
                loadUsers(); // 重新加载用户列表

            } catch (error) {
                console.error('重置密码失败:', error);
                showMessage('❌ 重置密码失败: ' + error.message, 'error');
            }
        }

        async function toggleUserStatus(userId, currentStatus) {
            const action = currentStatus ? '禁用' : '启用';
            
            if (!confirm(`确定要${action}这个用户吗？`)) {
                return;
            }

            try {
                const { error } = await supabase
                    .from('users')
                    .update({ is_active: !currentStatus })
                    .eq('id', userId);

                if (error) {
                    throw error;
                }

                showMessage(`✅ 用户${action}成功`, 'success');
                loadUsers();

            } catch (error) {
                console.error(`${action}用户失败:`, error);
                showMessage(`❌ ${action}用户失败: ` + error.message, 'error');
            }
        }

        function showBatchReset() {
            const defaultPassword = prompt('请输入要为所有用户设置的默认密码:', 'password123');
            if (!defaultPassword) return;

            if (!confirm(`确定要将所有用户的密码重置为 "${defaultPassword}" 吗？`)) {
                return;
            }

            batchResetPasswords(defaultPassword);
        }

        async function batchResetPasswords(password) {
            try {
                showMessage('正在批量重置密码...', 'info');

                const { data: users, error: queryError } = await supabase
                    .from('users')
                    .select('id, username');

                if (queryError) {
                    throw queryError;
                }

                const hashedPassword = hashPassword(password);
                let successCount = 0;

                for (const user of users) {
                    try {
                        const { error } = await supabase
                            .from('users')
                            .update({ password_hash: hashedPassword })
                            .eq('id', user.id);

                        if (!error) {
                            successCount++;
                        }
                    } catch (error) {
                        console.error(`重置用户 ${user.username} 密码失败:`, error);
                    }
                }

                showMessage(`✅ 批量重置完成！成功重置 ${successCount}/${users.length} 个用户的密码`, 'success');
                loadUsers();

            } catch (error) {
                console.error('批量重置失败:', error);
                showMessage('❌ 批量重置失败: ' + error.message, 'error');
            }
        }

        // 页面加载时自动加载用户
        window.addEventListener('load', function() {
            setTimeout(() => {
                if (typeof supabase !== 'undefined') {
                    loadUsers();
                } else {
                    showMessage('❌ Supabase未正确加载，请刷新页面', 'error');
                }
            }, 1000);
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('passwordModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
