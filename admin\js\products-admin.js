// 产品管理专用功能

let allProducts = [];
let filteredProducts = [];
let sortableInstance = null;
let isSortMode = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (checkAdminAuth()) {
        initializeProductsPage();
    }
});

// 初始化产品页面
async function initializeProductsPage() {
    await loadProducts();
    initializeSearch();
    createModals();
    loadSortSettings();
}

// 加载产品数据
async function loadProducts() {
    try {
        console.log('后台开始加载产品数据...');

        // 使用前端的getProducts函数，确保应用排序设置
        allProducts = await getProducts();

        console.log('后台加载到的产品数据:', allProducts);

        filteredProducts = [...allProducts];
        displayProducts();
        updateProductsCount();

    } catch (error) {
        console.error('加载产品失败:', error);
        showError('加载产品数据失败');
    }
}

// 显示产品列表
function displayProducts() {
    console.log('displayProducts被调用，产品数量:', filteredProducts.length);

    const tbody = document.getElementById('products-tbody');

    if (filteredProducts.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #666;">暂无产品数据</td></tr>';
        return;
    }

    // 检查产品是否有图片
    filteredProducts.forEach((product, index) => {
        console.log(`产品${index + 1}: ${product.product_name}, 图片URL: ${product.product_image || '无图片'}`);
    });
    
    const html = filteredProducts.map((product, index) => `
        <tr class="product-row" data-product-id="${product.id}" data-sort-order="${index}">
            <td>
                <span class="drag-handle" style="cursor: move; color: #666;">⋮⋮</span>
                <span style="margin-left: 10px; color: #999; font-size: 12px;">${index + 1}</span>
            </td>
            <td>
                ${product.product_image ?
                    `<div style="position: relative; display: inline-block;">
                        <img src="${getImageUrl(product.product_image)}" alt="${product.product_name}" class="product-image"
                             onerror="this.src='../placeholder.svg'; this.onerror=null;">
                        <button class="action-btn btn-danger" onclick="deleteProductImage('${product.id}')"
                                style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; border-radius: 50%; padding: 0; font-size: 10px; line-height: 1;"
                                title="删除图片">×</button>
                    </div>` :
                    '<div style="width: 50px; height: 50px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #999;">无图片</div>'
                }
            </td>
            <td>
                ${product.attachment_path ?
                    `<div style="position: relative; display: inline-block;">
                        <a href="${product.attachment_path}" target="_blank" style="display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: #e3f2fd; border-radius: 4px; text-decoration: none; color: #1976d2;">
                            <span style="font-size: 20px;">📄</span>
                        </a>
                        <button class="action-btn btn-danger" onclick="deleteProductPdf('${product.id}')"
                                style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; border-radius: 50%; padding: 0; font-size: 10px; line-height: 1;"
                                title="删除PDF">×</button>
                    </div>` :
                    '<div style="width: 50px; height: 50px; background: #f0f0f0; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #999;">无PDF</div>'
                }
            </td>
            <td><strong>${product.data_id}</strong></td>
            <td>${product.product_name}</td>
            <td><span style="background: #e9ecef; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${product.product_category}</span></td>
            <td>${product.specifications}</td>
            <td>${product.material}</td>
            <td><code style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-size: 11px;">${product.stock_code}</code></td>
            <td>
                <div class="actions">
                    <button class="action-btn btn-primary" onclick="editProduct('${product.id}')" title="编辑">✏️</button>
                    <button class="action-btn btn-success" onclick="viewProduct('${product.id}')" title="查看">👁️</button>
                    <button class="action-btn btn-danger" onclick="deleteProduct('${product.id}')" title="删除">🗑️</button>
                </div>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = html;
    
    // 如果是排序模式，重新初始化拖拽
    if (isSortMode && sortableInstance) {
        sortableInstance.destroy();
        initializeSortable();
    }
}

// 初始化拖拽排序
function initializeSortable() {
    const tbody = document.getElementById('products-tbody');
    sortableInstance = Sortable.create(tbody, {
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        onEnd: function(evt) {
            updateSortOrder();
        }
    });
}

// 切换排序模式
function toggleSortMode() {
    const toggleBtn = document.getElementById('sort-toggle');
    
    if (isSortMode) {
        // 退出排序模式
        isSortMode = false;
        toggleBtn.textContent = '🔄 启用排序';
        toggleBtn.className = 'btn btn-secondary';
        
        if (sortableInstance) {
            sortableInstance.destroy();
            sortableInstance = null;
        }
        
        // 隐藏拖拽手柄
        document.querySelectorAll('.drag-handle').forEach(handle => {
            handle.style.display = 'none';
        });
        
    } else {
        // 进入排序模式
        isSortMode = true;
        toggleBtn.textContent = '💾 保存排序';
        toggleBtn.className = 'btn btn-success';
        
        initializeSortable();
        
        // 显示拖拽手柄
        document.querySelectorAll('.drag-handle').forEach(handle => {
            handle.style.display = 'inline';
        });
        
        showSuccess('拖拽模式已启用，拖动产品行可调整顺序');
    }
}

// 更新排序顺序
async function updateSortOrder() {
    const rows = document.querySelectorAll('.product-row');
    const updates = [];
    
    rows.forEach((row, index) => {
        const productId = row.dataset.productId;
        updates.push({
            id: productId,
            sort_order: index
        });
    });
    
    try {
        // 批量更新排序
        for (const update of updates) {
            const { error } = await supabase
                .from('products')
                .update({ sort_order: update.sort_order })
                .eq('id', update.id);
            
            if (error) throw error;
        }
        
        showSuccess('排序已保存');
        
    } catch (error) {
        console.error('保存排序失败:', error);
        showError('保存排序失败');
        // 重新加载数据
        loadProducts();
    }
}

// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    
    // 防抖搜索
    const debouncedSearch = debounce(searchProducts, 300);
    searchInput.addEventListener('input', debouncedSearch);
    
    // 回车搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchProducts();
        }
    });
}

// 搜索产品
function searchProducts() {
    const searchTerm = document.getElementById('search-input').value.trim().toLowerCase();
    
    if (!searchTerm) {
        filteredProducts = [...allProducts];
    } else {
        filteredProducts = allProducts.filter(product => 
            product.product_name.toLowerCase().includes(searchTerm) ||
            product.data_id.toLowerCase().includes(searchTerm) ||
            product.stock_code.toLowerCase().includes(searchTerm) ||
            product.specifications.toLowerCase().includes(searchTerm) ||
            product.material.toLowerCase().includes(searchTerm)
        );
    }
    
    displayProducts();
    updateProductsCount();
}

// 重置搜索
function resetSearch() {
    document.getElementById('search-input').value = '';
    filteredProducts = [...allProducts];
    displayProducts();
    updateProductsCount();
}

// 更新产品数量显示
function updateProductsCount() {
    const countElement = document.getElementById('products-count');
    if (countElement) {
        countElement.textContent = filteredProducts.length;
    }
}

// 显示添加产品模态框
function showAddModal() {
    document.getElementById('product-form').reset();
    document.getElementById('product-id').value = '';
    document.getElementById('modal-title').textContent = '添加产品';
    showModal('product-modal');
}

// 编辑产品
async function editProduct(productId) {
    try {
        const { data: product, error } = await supabase
            .from('products')
            .select('*')
            .eq('id', productId)
            .single();
        
        if (error) throw error;
        
        // 填充表单
        fillProductForm(product);
        // 加载文件
        loadProductFiles(product);
        document.getElementById('modal-title').textContent = '编辑产品';
        showModal('product-modal');
        
    } catch (error) {
        console.error('加载产品详情失败:', error);
        showError('加载产品详情失败');
    }
}

// 填充产品表单
function fillProductForm(product) {
    const fields = [
        'data_id', 'stock_code', 'product_name', 'product_category',
        'specifications', 'material', 'thickness', 'remarks',
        'shape_code', 'main_process_1', 'main_process_2', 'main_process_3',
        'main_process_4', 'process_count', 'variable_process_1',
        'variable_process_2', 'variable_process_3', 'attachment_path'
    ];
    
    fields.forEach(field => {
        const element = document.getElementById(field);
        if (element) {
            element.value = product[field] || '';
        }
    });
    
    document.getElementById('product-id').value = product.id;
}

// 查看产品详情
async function viewProduct(productId) {
    try {
        const { data: product, error } = await supabase
            .from('products')
            .select('*')
            .eq('id', productId)
            .single();
        
        if (error) throw error;
        
        // 显示产品详情
        showProductDetails(product);
        
    } catch (error) {
        console.error('加载产品详情失败:', error);
        showError('加载产品详情失败');
    }
}

// 显示产品详情
function showProductDetails(product) {
    const detailsHtml = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h4>基本信息</h4>
                <p><strong>数据ID:</strong> ${product.data_id}</p>
                <p><strong>存货编码:</strong> ${product.stock_code}</p>
                <p><strong>产品名称:</strong> ${product.product_name}</p>
                <p><strong>产品类别:</strong> ${product.product_category}</p>
                <p><strong>规格:</strong> ${product.specifications}</p>
                <p><strong>材质:</strong> ${product.material}</p>
                <p><strong>料厚:</strong> ${product.thickness}</p>
            </div>
            <div>
                <h4>工艺信息</h4>
                <p><strong>外形编码:</strong> ${product.shape_code || '-'}</p>
                <p><strong>主要工艺1:</strong> ${product.main_process_1 || '-'}</p>
                <p><strong>主要工艺2:</strong> ${product.main_process_2 || '-'}</p>
                <p><strong>主要工艺3:</strong> ${product.main_process_3 || '-'}</p>
                <p><strong>工序数:</strong> ${product.process_count || '-'}</p>
                <p><strong>附件路径:</strong> ${product.attachment_path || '-'}</p>
            </div>
        </div>
        ${product.remarks ? `<div style="margin-top: 20px;"><h4>备注</h4><p>${product.remarks}</p></div>` : ''}
    `;
    
    document.getElementById('product-details').innerHTML = detailsHtml;
    showModal('product-details-modal');
}

// 删除产品
async function deleteProduct(productId) {
    console.log('删除产品被调用，产品ID:', productId);

    if (!confirm('确定要删除这个产品吗？此操作不可恢复，同时会删除相关的下载记录。')) {
        return;
    }

    try {
        console.log('开始删除产品...');

        // 首先删除相关的下载记录
        console.log('删除相关下载记录...');
        const { error: downloadError } = await supabase
            .from('download_records')
            .delete()
            .eq('product_id', productId);

        if (downloadError) {
            console.error('删除下载记录失败:', downloadError);
            throw downloadError;
        }

        // 然后删除产品
        console.log('删除产品记录...');
        const { error: productError } = await supabase
            .from('products')
            .delete()
            .eq('id', productId);

        if (productError) {
            console.error('删除产品数据库错误:', productError);
            throw productError;
        }

        console.log('产品删除成功');
        showSuccess('产品删除成功');

        // 重新加载产品列表
        await loadProducts();

    } catch (error) {
        console.error('删除产品失败:', error);
        showError('删除产品失败: ' + error.message);
    }
}

// 保存产品
async function saveProduct() {
    const form = document.getElementById('product-form');
    const formData = new FormData(form);
    const productId = document.getElementById('product-id').value;
    
    // 构建产品数据
    const productData = {};
    for (let [key, value] of formData.entries()) {
        if (key !== 'product-id') {
            productData[key] = value.trim();
        }
    }

    console.log('保存产品数据:', productData);
    console.log('图片URL:', productData.product_image);
    console.log('PDF URL:', productData.attachment_path);
    
    // 验证必填字段
    const requiredFields = ['data_id', 'stock_code', 'product_name', 'product_category', 'specifications', 'material'];
    const missingFields = requiredFields.filter(field => !productData[field]);
    
    if (missingFields.length > 0) {
        showError(`请填写必填字段: ${missingFields.join(', ')}`);
        return;
    }
    
    try {
        let result;
        
        if (productId) {
            // 更新产品
            result = await supabase
                .from('products')
                .update(productData)
                .eq('id', productId);
        } else {
            // 添加产品
            result = await supabase
                .from('products')
                .insert([productData]);
        }
        
        if (result.error) throw result.error;
        
        showSuccess(productId ? '产品更新成功' : '产品添加成功');
        hideModal('product-modal');
        loadProducts();
        
    } catch (error) {
        console.error('保存产品失败:', error);
        showError('保存产品失败: ' + error.message);
    }
}

// 下载Excel模板
function downloadTemplate() {
    const headers = [
        '数据ID', '存货编码', '产品名称', '产品类别', '产品规格', '材质', '料厚',
        '备注', '外形编码', '主要工艺1', '主要工艺2', '主要工艺3', '主要工艺4',
        '工序数', '可变加工艺1', '可变加工艺2', '可变加工艺3'
    ];
    
    const sampleData = [
        'A0005', 'CS0005', '示例产品', 'DB', 'φ80', 'SPHC', '3.0',
        '示例备注', '1234', '外径φ60', '内孔φ30', '', '',
        '2', '打焊点', '', '', 'pdfs/A0005_示例产品.pdf'
    ];
    
    const csvContent = [
        headers.join(','),
        sampleData.map(field => `"${field}"`).join(',')
    ].join('\n');
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '产品导入模板.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showSuccess('模板下载成功');
}

// 导出产品数据
function exportProducts() {
    if (filteredProducts.length === 0) {
        showError('没有数据可导出');
        return;
    }

    const exportData = filteredProducts.map(product => ({
        '数据ID': product.data_id,
        '存货编码': product.stock_code,
        '产品名称': product.product_name,
        '产品类别': product.product_category,
        '产品规格': product.specifications,
        '材质': product.material,
        '料厚': product.thickness,
        '备注': product.remarks || '',
        '外形编码': product.shape_code || '',
        '主要工艺1': product.main_process_1 || '',
        '主要工艺2': product.main_process_2 || '',
        '主要工艺3': product.main_process_3 || '',
        '主要工艺4': product.main_process_4 || '',
        '工序数': product.process_count || '',
        '可变加工艺1': product.variable_process_1 || '',
        '可变加工艺2': product.variable_process_2 || '',
        '可变加工艺3': product.variable_process_3 || '',
        '附件路径': product.attachment_path || ''
    }));

    exportToCSV(exportData, `产品数据_${new Date().toISOString().split('T')[0]}.csv`);
}

// 批量导入相关变量
let importData = [];

// 显示批量导入模态框
function showImportModal() {
    // 重置导入状态
    document.getElementById('import-preview').style.display = 'none';
    document.getElementById('import-progress').style.display = 'none';
    document.getElementById('import-file').value = '';
    importData = [];

    showModal('import-modal');

    // 设置拖拽事件
    setupDragAndDrop();
}

// 设置拖拽上传
function setupDragAndDrop() {
    const importArea = document.getElementById('import-area');

    importArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        importArea.classList.add('dragover');
    });

    importArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        importArea.classList.remove('dragover');
    });

    importArea.addEventListener('drop', function(e) {
        e.preventDefault();
        importArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImportFile({ files: files });
        }
    });
}

// 处理导入文件
async function handleImportFile(input) {
    const file = input.files[0];
    if (!file) return;

    const fileName = file.name.toLowerCase();
    if (!fileName.endsWith('.csv') && !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        showError('请选择CSV或Excel格式的文件');
        return;
    }

    try {
        if (fileName.endsWith('.csv')) {
            const content = await handleFileUpload(file, ['text/csv'], 5 * 1024 * 1024);
            parseCSVContent(content);
        } else {
            // Excel文件处理
            showError('Excel文件支持正在开发中，请使用CSV格式的模板文件');
        }
    } catch (error) {
        showError('文件读取失败: ' + error.message);
    }
}

// 解析CSV内容
function parseCSVContent(content) {
    const lines = content.split('\n').filter(line => line.trim());

    if (lines.length < 2) {
        showError('文件内容不正确，至少需要包含表头和一行数据');
        return;
    }

    // 解析表头
    const headers = parseCSVLine(lines[0]);
    const expectedHeaders = [
        '数据ID', '存货编码', '产品名称', '产品类别', '产品规格', '材质', '料厚',
        '备注', '外形编码', '主要工艺1', '主要工艺2', '主要工艺3', '主要工艺4',
        '工序数', '可变加工艺1', '可变加工艺2', '可变加工艺3'
    ];

    // 验证表头
    const missingHeaders = expectedHeaders.filter(header => !headers.includes(header));
    if (missingHeaders.length > 0) {
        showError(`缺少必要的列: ${missingHeaders.join(', ')}`);
        return;
    }

    // 解析数据行
    importData = [];
    const errors = [];

    for (let i = 1; i < lines.length; i++) {
        const values = parseCSVLine(lines[i]);
        if (values.length !== headers.length) {
            errors.push(`第${i + 1}行数据列数不匹配`);
            continue;
        }

        const rowData = {};
        headers.forEach((header, index) => {
            rowData[header] = values[index];
        });

        // 验证必填字段
        const requiredFields = ['数据ID', '存货编码', '产品名称', '产品类别', '产品规格', '材质'];
        const missingFields = requiredFields.filter(field => !rowData[field] || !rowData[field].trim());

        if (missingFields.length > 0) {
            errors.push(`第${i + 1}行缺少必填字段: ${missingFields.join(', ')}`);
            continue;
        }

        importData.push(rowData);
    }

    if (errors.length > 0) {
        showError(`数据验证失败:\n${errors.slice(0, 5).join('\n')}${errors.length > 5 ? '\n...' : ''}`);
        return;
    }

    if (importData.length === 0) {
        showError('没有有效的数据行');
        return;
    }

    // 显示预览
    showImportPreview();
}

// 解析CSV行
function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
            if (inQuotes && line[i + 1] === '"') {
                current += '"';
                i++; // 跳过下一个引号
            } else {
                inQuotes = !inQuotes;
            }
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }

    result.push(current.trim());
    return result;
}

// 显示导入预览
function showImportPreview() {
    const previewContent = document.getElementById('preview-content');

    let html = `
        <p><strong>将导入 ${importData.length} 条产品数据</strong></p>
        <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
            <thead>
                <tr style="background: #f8f9fa;">
                    <th style="border: 1px solid #ddd; padding: 5px;">数据ID</th>
                    <th style="border: 1px solid #ddd; padding: 5px;">产品名称</th>
                    <th style="border: 1px solid #ddd; padding: 5px;">类别</th>
                    <th style="border: 1px solid #ddd; padding: 5px;">规格</th>
                    <th style="border: 1px solid #ddd; padding: 5px;">材质</th>
                </tr>
            </thead>
            <tbody>
    `;

    importData.slice(0, 10).forEach(row => {
        html += `
            <tr>
                <td style="border: 1px solid #ddd; padding: 5px;">${row['数据ID']}</td>
                <td style="border: 1px solid #ddd; padding: 5px;">${row['产品名称']}</td>
                <td style="border: 1px solid #ddd; padding: 5px;">${row['产品类别']}</td>
                <td style="border: 1px solid #ddd; padding: 5px;">${row['产品规格']}</td>
                <td style="border: 1px solid #ddd; padding: 5px;">${row['材质']}</td>
            </tr>
        `;
    });

    html += '</tbody></table>';

    if (importData.length > 10) {
        html += `<p style="color: #666; font-size: 12px; margin-top: 10px;">仅显示前10条，共${importData.length}条数据</p>`;
    }

    previewContent.innerHTML = html;
    document.getElementById('import-preview').style.display = 'block';
}

// 确认导入
async function confirmImport() {
    if (importData.length === 0) {
        showError('没有数据可导入');
        return;
    }

    // 显示进度
    document.getElementById('import-preview').style.display = 'none';
    document.getElementById('import-progress').style.display = 'block';

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    try {
        for (let i = 0; i < importData.length; i++) {
            const row = importData[i];

            // 更新进度
            document.getElementById('progress-info').textContent =
                `正在导入第 ${i + 1} / ${importData.length} 条数据...`;

            try {
                // 转换字段名
                const productData = {
                    data_id: row['数据ID'],
                    stock_code: row['存货编码'],
                    product_name: row['产品名称'],
                    product_category: row['产品类别'],
                    specifications: row['产品规格'],
                    material: row['材质'],
                    thickness: row['料厚'] || '',
                    remarks: row['备注'] || '',
                    shape_code: row['外形编码'] || '',
                    main_process_1: row['主要工艺1'] || '',
                    main_process_2: row['主要工艺2'] || '',
                    main_process_3: row['主要工艺3'] || '',
                    main_process_4: row['主要工艺4'] || '',
                    process_count: row['工序数'] ? parseInt(row['工序数']) : null,
                    variable_process_1: row['可变加工艺1'] || '',
                    variable_process_2: row['可变加工艺2'] || '',
                    variable_process_3: row['可变加工艺3'] || ''
                };

                const { error } = await supabase
                    .from('products')
                    .insert([productData]);

                if (error) throw error;

                successCount++;

            } catch (error) {
                errorCount++;
                errors.push(`第${i + 1}行: ${error.message}`);
            }

            // 添加小延迟避免过快请求
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // 显示结果
        hideModal('import-modal');

        if (successCount > 0) {
            showSuccess(`导入完成！成功导入 ${successCount} 条数据${errorCount > 0 ? `，失败 ${errorCount} 条` : ''}`);
            loadProducts(); // 重新加载产品列表
        }

        if (errorCount > 0) {
            console.error('导入错误:', errors);
            showError(`部分数据导入失败，请检查数据格式。失败 ${errorCount} 条`);
        }

    } catch (error) {
        console.error('批量导入失败:', error);
        showError('批量导入失败: ' + error.message);
        hideModal('import-modal');
    }
}

// 取消导入
function cancelImport() {
    document.getElementById('import-preview').style.display = 'none';
    document.getElementById('import-file').value = '';
    importData = [];
}

// ==================== 排序功能 ====================

// 加载排序设置
function loadSortSettings() {
    const savedSortField = localStorage.getItem('products_sort_field') || 'created_at';
    const savedSortOrder = localStorage.getItem('products_sort_order') || 'desc';

    document.getElementById('sort-field').value = savedSortField;
    document.getElementById('sort-order').value = savedSortOrder;

    // 应用排序
    applySorting();
}

// 应用排序
function applySorting() {
    const sortField = document.getElementById('sort-field').value;
    const sortOrder = document.getElementById('sort-order').value;

    // 对产品数组进行排序
    filteredProducts.sort((a, b) => {
        let valueA = a[sortField];
        let valueB = b[sortField];

        // 处理不同数据类型
        if (sortField === 'created_at' || sortField === 'updated_at') {
            valueA = new Date(valueA);
            valueB = new Date(valueB);
        } else if (typeof valueA === 'string') {
            valueA = valueA.toLowerCase();
            valueB = valueB.toLowerCase();
        }

        // 比较值
        let comparison = 0;
        if (valueA > valueB) {
            comparison = 1;
        } else if (valueA < valueB) {
            comparison = -1;
        }

        // 根据排序方向返回结果
        return sortOrder === 'asc' ? comparison : -comparison;
    });

    // 重新显示产品
    displayProducts();
}

// 保存排序设置
function saveSortSettings() {
    const sortField = document.getElementById('sort-field').value;
    const sortOrder = document.getElementById('sort-order').value;

    localStorage.setItem('products_sort_field', sortField);
    localStorage.setItem('products_sort_order', sortOrder);

    showSuccess('排序设置已保存');
}

// 切换拖拽排序模式
function toggleSortMode() {
    const toggleBtn = document.getElementById('sort-toggle');
    const tbody = document.getElementById('products-tbody');

    if (isSortMode) {
        // 禁用拖拽排序
        if (sortableInstance) {
            sortableInstance.destroy();
            sortableInstance = null;
        }
        isSortMode = false;
        toggleBtn.textContent = '🔄 启用拖拽排序';
        toggleBtn.classList.remove('btn-warning');
        toggleBtn.classList.add('btn-secondary');
        showSuccess('拖拽排序已禁用');
    } else {
        // 启用拖拽排序
        sortableInstance = new Sortable(tbody, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            onEnd: function(evt) {
                // 更新产品顺序
                updateProductOrder(evt.oldIndex, evt.newIndex);
            }
        });
        isSortMode = true;
        toggleBtn.textContent = '❌ 禁用拖拽排序';
        toggleBtn.classList.remove('btn-secondary');
        toggleBtn.classList.add('btn-warning');
        showSuccess('拖拽排序已启用，可以拖拽行来调整顺序');
    }
}

// 更新产品顺序
function updateProductOrder(oldIndex, newIndex) {
    // 移动数组中的元素
    const movedProduct = filteredProducts.splice(oldIndex, 1)[0];
    filteredProducts.splice(newIndex, 0, movedProduct);

    // 这里可以添加保存到数据库的逻辑
    // 例如更新每个产品的sort_order字段
    showSuccess('产品顺序已更新');
}

// ==================== 文件处理辅助函数 ====================

// 处理文件上传的通用函数
async function handleFileUpload(file, allowedTypes, maxSize) {
    return new Promise((resolve, reject) => {
        // 验证文件类型
        if (allowedTypes && !allowedTypes.includes(file.type)) {
            reject(new Error(`不支持的文件类型: ${file.type}`));
            return;
        }

        // 验证文件大小
        if (maxSize && file.size > maxSize) {
            reject(new Error(`文件大小超过限制: ${(maxSize / 1024 / 1024).toFixed(1)}MB`));
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsText(file);
    });
}

// ==================== 文件处理辅助函数 ====================

// 处理文件上传的通用函数
async function handleFileUpload(file, allowedTypes, maxSize) {
    return new Promise((resolve, reject) => {
        // 验证文件类型
        if (allowedTypes && !allowedTypes.includes(file.type)) {
            reject(new Error(`不支持的文件类型: ${file.type}`));
            return;
        }

        // 验证文件大小
        if (maxSize && file.size > maxSize) {
            reject(new Error(`文件大小超过限制: ${(maxSize / 1024 / 1024).toFixed(1)}MB`));
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsText(file);
    });
}

// ==================== 图片和文件管理功能 ====================

// 获取正确的图片URL
function getImageUrl(imageUrl) {
    if (!imageUrl) return '../placeholder.svg';

    // 如果是完整的URL（以http开头），直接返回
    if (imageUrl.startsWith('http')) {
        return imageUrl;
    }

    // 如果是相对路径，转换为绝对路径
    if (imageUrl.startsWith('../')) {
        // 从管理后台访问，需要调整路径
        return imageUrl.replace('../', '../');
    }

    // 如果只是文件名，假设在images目录中
    if (!imageUrl.includes('/')) {
        return `../images/${imageUrl}`;
    }

    // 其他情况直接返回
    return imageUrl;
}

// 调试：确认文件已加载
console.log('products-admin.js 图片管理功能已加载');

// 处理单个产品图片上传
async function handleImageUpload(input) {
    const file = input.files[0];
    if (!file) return;

    // 验证文件类型和大小
    if (!file.type.startsWith('image/')) {
        showError('请选择有效的图片文件');
        return;
    }

    if (file.size > 5 * 1024 * 1024) {
        showError('图片文件不能超过5MB');
        return;
    }

    try {
        // 显示处理进度
        showSuccess('正在处理图片...');

        // 生成文件名
        const dataId = document.getElementById('data_id').value || 'temp';
        const fileExt = file.name.split('.').pop();
        const fileName = `${dataId}_${Date.now()}.${fileExt}`;
        const relativePath = `../images/${fileName}`;

        // 读取文件并创建预览
        const reader = new FileReader();
        reader.onload = function(e) {
            // 更新表单字段
            document.getElementById('product_image').value = relativePath;

            // 显示预览
            showImagePreview(e.target.result);

            // 创建下载链接提示用户保存文件
            const link = document.createElement('a');
            link.href = e.target.result;
            link.download = fileName;

            // 提示用户
            showSuccess(`图片已处理，请将下载的文件 "${fileName}" 保存到项目的 images 目录中`);

            // 自动下载文件
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        };

        reader.readAsDataURL(file);

    } catch (error) {
        console.error('图片处理失败:', error);
        showError('图片处理失败: ' + error.message);
    }
}

// 清理文件名，移除中文字符和特殊字符
function sanitizeFileName(fileName) {
    // 中文到英文的映射
    const chineseToEnglish = {
        '技术文档': 'technical_doc',
        '说明书': 'manual',
        '产品手册': 'product_manual',
        '工艺文档': 'process_doc',
        '检验标准': 'inspection_standard',
        '用户指南': 'user_guide',
        '安装说明': 'installation_guide'
    };

    // 替换中文词汇
    let cleanName = fileName;
    for (const [chinese, english] of Object.entries(chineseToEnglish)) {
        cleanName = cleanName.replace(chinese, english);
    }

    // 移除其他中文字符和特殊字符，只保留字母、数字、下划线、连字符和点
    cleanName = cleanName.replace(/[^\w\-_.]/g, '_');

    // 移除多余的下划线
    cleanName = cleanName.replace(/_+/g, '_');

    return cleanName;
}

// 处理PDF上传 - 使用Supabase存储
async function handlePdfUpload(input) {
    const file = input.files[0];
    if (!file) return;

    // 验证文件类型和大小
    if (file.type !== 'application/pdf') {
        showError('请选择有效的PDF文件');
        return;
    }

    if (file.size > 10 * 1024 * 1024) {
        showError('PDF文件不能超过10MB');
        return;
    }

    try {
        // 显示上传进度
        showSuccess('正在上传PDF...');

        // 生成文件名：使用原始文件名并清理中文字符
        const dataId = document.getElementById('data_id').value || 'temp';
        const originalName = file.name.replace('.pdf', '');
        const cleanOriginalName = sanitizeFileName(originalName);
        const fileName = `${dataId}_${cleanOriginalName}.pdf`;
        const filePath = `pdfs/${fileName}`;

        // 上传到Supabase Storage
        const { error } = await supabase.storage
            .from('product-pdfs')
            .upload(filePath, file, {
                cacheControl: '3600',
                upsert: true
            });

        if (error) throw error;

        // 获取公共URL
        const { data: urlData } = supabase.storage
            .from('product-pdfs')
            .getPublicUrl(filePath);

        // 更新表单
        document.getElementById('attachment_path').value = urlData.publicUrl;

        // 显示预览
        showPdfPreview(fileName);

        showSuccess('PDF上传成功');

    } catch (error) {
        console.error('PDF上传失败:', error);
        showError('PDF上传失败: ' + error.message);
    }
}

// 显示图片预览
function showImagePreview(imageUrl) {
    const currentImageDiv = document.getElementById('current-image');
    const uploadArea = document.getElementById('upload-area');
    const preview = document.getElementById('current-image-preview');

    preview.src = imageUrl;
    currentImageDiv.style.display = 'block';
    uploadArea.style.display = 'none';
}

// 显示PDF预览
function showPdfPreview(fileName) {
    const currentPdfDiv = document.getElementById('current-pdf');
    const uploadArea = document.getElementById('pdf-upload-area-inner');
    const pdfName = document.getElementById('current-pdf-name');

    if (pdfName) pdfName.textContent = fileName;
    if (currentPdfDiv) currentPdfDiv.style.display = 'block';
    if (uploadArea) uploadArea.style.display = 'none';
}

// 删除当前图片
async function deleteCurrentImage() {
    const imageUrl = document.getElementById('product_image').value;

    if (!imageUrl) return;

    if (!confirm('确定要删除当前图片吗？')) return;

    try {
        // 从URL中提取文件路径
        const urlParts = imageUrl.split('/');
        const fileName = urlParts[urlParts.length - 1];
        const filePath = `images/${fileName}`;

        // 从Storage中删除文件
        const { error } = await supabase.storage
            .from('product-images')
            .remove([filePath]);

        if (error) {
            console.warn('删除Storage文件失败:', error);
        }

        // 清除表单值
        document.getElementById('product_image').value = '';

        // 隐藏预览，显示上传区域
        document.getElementById('current-image').style.display = 'none';
        document.getElementById('upload-area').style.display = 'block';

        showSuccess('图片已删除');

    } catch (error) {
        console.error('删除图片失败:', error);
        showError('删除图片失败: ' + error.message);
    }
}

// 删除当前PDF
async function deleteCurrentPdf() {
    const pdfUrl = document.getElementById('attachment_path').value;

    if (!pdfUrl) return;

    if (!confirm('确定要删除当前PDF吗？')) return;

    try {
        // 从URL中提取文件路径
        const urlParts = pdfUrl.split('/');
        const fileName = urlParts[urlParts.length - 1];
        const filePath = `pdfs/${fileName}`;

        // 从Storage中删除文件
        const { error } = await supabase.storage
            .from('product-pdfs')
            .remove([filePath]);

        if (error) {
            console.warn('删除Storage文件失败:', error);
        }

        // 清除表单值
        document.getElementById('attachment_path').value = '';

        // 隐藏预览，显示上传区域
        document.getElementById('current-pdf').style.display = 'none';
        document.getElementById('pdf-upload-area-inner').style.display = 'block';

        showSuccess('PDF已删除');

    } catch (error) {
        console.error('删除PDF失败:', error);
        showError('删除PDF失败: ' + error.message);
    }
}

// 删除产品图片（从产品列表中）
async function deleteProductImage(productId) {
    if (!confirm('确定要删除这个产品的图片吗？')) return;

    try {
        // 获取产品信息
        const product = allProducts.find(p => p.id == productId);
        if (!product || !product.product_image) {
            showError('产品图片不存在');
            return;
        }

        // 从URL中提取文件路径
        const urlParts = product.product_image.split('/');
        const fileName = urlParts[urlParts.length - 1];
        const filePath = `images/${fileName}`;

        // 从Storage中删除文件
        const { error: storageError } = await supabase.storage
            .from('product-images')
            .remove([filePath]);

        if (storageError) {
            console.warn('删除Storage文件失败:', storageError);
        }

        // 更新数据库记录
        const { error: dbError } = await supabase
            .from('products')
            .update({ product_image: null })
            .eq('id', productId);

        if (dbError) throw dbError;

        // 重新加载产品列表
        await loadProducts();

        showSuccess('产品图片已删除');

    } catch (error) {
        console.error('删除产品图片失败:', error);
        showError('删除产品图片失败: ' + error.message);
    }
}

// 删除产品PDF（从产品列表中）
async function deleteProductPdf(productId) {
    if (!confirm('确定要删除这个产品的PDF吗？')) return;

    try {
        // 获取产品信息
        const product = allProducts.find(p => p.id == productId);
        if (!product || !product.attachment_path) {
            showError('产品PDF不存在');
            return;
        }

        // 从URL中提取文件路径
        const urlParts = product.attachment_path.split('/');
        const fileName = urlParts[urlParts.length - 1];
        const filePath = `pdfs/${fileName}`;

        // 从Storage中删除文件
        const { error: storageError } = await supabase.storage
            .from('product-pdfs')
            .remove([filePath]);

        if (storageError) {
            console.warn('删除Storage文件失败:', storageError);
        }

        // 更新数据库记录
        const { error: dbError } = await supabase
            .from('products')
            .update({ attachment_path: null })
            .eq('id', productId);

        if (dbError) throw dbError;

        // 重新加载产品列表
        await loadProducts();

        showSuccess('产品PDF已删除');

    } catch (error) {
        console.error('删除产品PDF失败:', error);
        showError('删除产品PDF失败: ' + error.message);
    }
}

// 编辑产品时加载现有的图片和PDF
function loadProductFiles(product) {
    // 加载图片
    if (product.product_image) {
        document.getElementById('product_image').value = product.product_image;
        showImagePreview(getImageUrl(product.product_image));
    } else {
        // 重置图片上传区域
        const currentImageDiv = document.getElementById('current-image');
        const uploadArea = document.getElementById('upload-area');
        if (currentImageDiv) currentImageDiv.style.display = 'none';
        if (uploadArea) uploadArea.style.display = 'block';
    }

    // 加载PDF
    if (product.attachment_path) {
        document.getElementById('attachment_path').value = product.attachment_path;
        const fileName = product.attachment_path.split('/').pop();
        showPdfPreview(fileName);
    } else {
        // 重置PDF上传区域
        const currentPdfDiv = document.getElementById('current-pdf');
        const pdfUploadArea = document.getElementById('pdf-upload-area-inner');
        if (currentPdfDiv) currentPdfDiv.style.display = 'none';
        if (pdfUploadArea) pdfUploadArea.style.display = 'block';
    }
}

// 调试：确认整个文件已加载完成
console.log('products-admin.js 完全加载完成，handleImageUpload函数已定义:', typeof handleImageUpload);
