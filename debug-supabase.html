<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase 连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Supabase 连接诊断工具</h1>
        
        <div class="test-section">
            <h2>1. 基础连接测试</h2>
            <button onclick="testConnection()">测试 Supabase 连接</button>
            <div id="connection-result"></div>
        </div>

        <div class="test-section">
            <h2>2. 产品数据查询测试</h2>
            <button onclick="testProductQuery()">查询产品数据</button>
            <div id="product-result"></div>
        </div>

        <div class="test-section">
            <h2>3. RLS 策略测试</h2>
            <button onclick="testRLS()">测试 RLS 策略</button>
            <div id="rls-result"></div>
        </div>

        <div class="test-section">
            <h2>4. 网络连接测试</h2>
            <button onclick="testNetwork()">测试网络连接</button>
            <div id="network-result"></div>
        </div>

        <div class="test-section">
            <h2>5. 控制台日志</h2>
            <button onclick="clearLogs()">清除日志</button>
            <pre id="console-logs"></pre>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <script>
        // 重写console.log来捕获日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        let logs = [];
        
        function addLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateLogDisplay();
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addLog('log', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            addLog('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            addLog('warn', ...args);
        };
        
        function updateLogDisplay() {
            document.getElementById('console-logs').textContent = logs.join('\n');
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }

        // Supabase 配置
        const SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';

        let supabase;

        // 测试连接
        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.innerHTML = '<div class="loading">正在测试连接...</div>';
            
            try {
                console.log('开始测试 Supabase 连接...');
                
                // 检查 Supabase 库是否加载
                if (typeof window.supabase === 'undefined') {
                    throw new Error('Supabase 库未加载');
                }
                
                // 创建客户端
                supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                console.log('Supabase 客户端创建成功');
                
                // 测试基本连接
                const { data, error } = await supabase.from('products').select('count', { count: 'exact', head: true });
                
                if (error) {
                    throw error;
                }
                
                resultDiv.innerHTML = `
                    <div class="success">✅ 连接成功！</div>
                    <pre>URL: ${SUPABASE_URL}
Key: ${SUPABASE_ANON_KEY.substring(0, 20)}...
产品表记录数: ${data || '未知'}</pre>
                `;
                
            } catch (error) {
                console.error('连接测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="error">❌ 连接失败</div>
                    <pre>错误信息: ${error.message}
错误详情: ${JSON.stringify(error, null, 2)}</pre>
                `;
            }
        }

        // 测试产品查询
        async function testProductQuery() {
            const resultDiv = document.getElementById('product-result');
            resultDiv.innerHTML = '<div class="loading">正在查询产品数据...</div>';
            
            try {
                if (!supabase) {
                    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                }
                
                console.log('开始查询产品数据...');
                
                const { data, error } = await supabase
                    .from('products')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(5);
                
                if (error) {
                    throw error;
                }
                
                console.log('产品查询成功:', data);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ 查询成功！找到 ${data.length} 个产品</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('产品查询失败:', error);
                resultDiv.innerHTML = `
                    <div class="error">❌ 查询失败</div>
                    <pre>错误信息: ${error.message}
错误详情: ${JSON.stringify(error, null, 2)}</pre>
                `;
            }
        }

        // 测试 RLS 策略
        async function testRLS() {
            const resultDiv = document.getElementById('rls-result');
            resultDiv.innerHTML = '<div class="loading">正在测试 RLS 策略...</div>';
            
            try {
                if (!supabase) {
                    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                }
                
                console.log('测试 RLS 策略...');
                
                // 测试匿名用户访问
                const { data: anonData, error: anonError } = await supabase
                    .from('products')
                    .select('id, product_name')
                    .limit(1);
                
                if (anonError) {
                    throw new Error(`匿名访问失败: ${anonError.message}`);
                }
                
                console.log('匿名用户访问成功:', anonData);
                
                resultDiv.innerHTML = `
                    <div class="success">✅ RLS 策略正常</div>
                    <pre>匿名用户可以访问产品数据
示例数据: ${JSON.stringify(anonData, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('RLS 测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="error">❌ RLS 策略有问题</div>
                    <pre>错误信息: ${error.message}</pre>
                `;
            }
        }

        // 测试网络连接
        async function testNetwork() {
            const resultDiv = document.getElementById('network-result');
            resultDiv.innerHTML = '<div class="loading">正在测试网络连接...</div>';
            
            try {
                console.log('测试网络连接...');
                
                // 测试到 Supabase 的连接
                const response = await fetch(SUPABASE_URL + '/rest/v1/', {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.text();
                console.log('网络连接成功');
                
                resultDiv.innerHTML = `
                    <div class="success">✅ 网络连接正常</div>
                    <pre>状态码: ${response.status}
响应: ${data.substring(0, 200)}...</pre>
                `;
                
            } catch (error) {
                console.error('网络连接失败:', error);
                resultDiv.innerHTML = `
                    <div class="error">❌ 网络连接失败</div>
                    <pre>错误信息: ${error.message}</pre>
                `;
            }
        }

        // 页面加载时自动运行基础测试
        window.addEventListener('load', function() {
            console.log('页面加载完成，开始诊断...');
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
