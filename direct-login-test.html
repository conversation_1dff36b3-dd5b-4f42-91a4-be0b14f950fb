<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 直接登录测试</h1>
        
        <div class="user-info">
            <h3>测试账户：</h3>
            <p><strong>用户名:</strong> tex</p>
            <p><strong>邮箱:</strong> <EMAIL></p>
            <p><strong>密码:</strong> 123456</p>
            <p><strong>权限:</strong> privileged (特许用户)</p>
        </div>
        
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456" placeholder="输入密码">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testDirectQuery()" style="background-color: #28a745;">直接查询用户</button>
        <button onclick="setPassword()" style="background-color: #ffc107; color: #000;">重新设置密码</button>
        
        <div id="message" class="message"></div>
    </div>

    <script>
        // Supabase配置
        const SUPABASE_URL = 'https://snckktsqwrbfwtjlvcfr.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644';

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = text;
            messageDiv.style.display = 'block';
        }

        // 密码哈希函数
        function hashPassword(password) {
            return btoa(password + 'chunsheng_salt');
        }

        // 验证密码
        function verifyPassword(password, hashedPassword) {
            return hashPassword(password) === hashedPassword;
        }

        // 直接使用fetch API查询Supabase
        async function supabaseQuery(table, options = {}) {
            const url = `${SUPABASE_URL}/rest/v1/${table}`;
            const headers = {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
            };

            // 构建查询参数
            const params = new URLSearchParams();
            if (options.select) params.append('select', options.select);
            if (options.eq) {
                for (const [key, value] of Object.entries(options.eq)) {
                    params.append(key, `eq.${value}`);
                }
            }
            if (options.single) params.append('limit', '1');

            const queryUrl = params.toString() ? `${url}?${params.toString()}` : url;

            const response = await fetch(queryUrl, {
                method: options.method || 'GET',
                headers: headers,
                body: options.body ? JSON.stringify(options.body) : undefined
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return options.single ? data[0] : data;
        }

        // 更新用户数据
        async function supabaseUpdate(table, data, conditions) {
            const url = `${SUPABASE_URL}/rest/v1/${table}`;
            const headers = {
                'apikey': SUPABASE_ANON_KEY,
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json',
                'Prefer': 'return=representation'
            };

            // 构建查询参数
            const params = new URLSearchParams();
            for (const [key, value] of Object.entries(conditions)) {
                params.append(key, `eq.${value}`);
            }

            const response = await fetch(`${url}?${params.toString()}`, {
                method: 'PATCH',
                headers: headers,
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        }

        async function testDirectQuery() {
            try {
                showMessage('正在查询用户数据...', 'info');

                const user = await supabaseQuery('users', {
                    select: '*',
                    eq: { email: '<EMAIL>' },
                    single: true
                });

                if (user) {
                    showMessage(`✅ 用户查询成功！<br>
                        用户名: ${user.username}<br>
                        邮箱: ${user.email}<br>
                        权限: ${user.user_type}<br>
                        密码哈希: ${user.password_hash || '未设置'}<br>
                        状态: ${user.is_active ? '活跃' : '禁用'}`, 'success');
                } else {
                    showMessage('❌ 未找到用户', 'error');
                }

            } catch (error) {
                console.error('查询失败:', error);
                showMessage('❌ 查询失败: ' + error.message, 'error');
            }
        }

        async function setPassword() {
            try {
                showMessage('正在设置密码...', 'info');

                const hashedPassword = hashPassword('123456');
                console.log('计算的密码哈希:', hashedPassword);

                await supabaseUpdate('users', 
                    { password_hash: hashedPassword },
                    { email: '<EMAIL>' }
                );

                showMessage(`✅ 密码设置成功！<br>
                    密码: 123456<br>
                    哈希值: ${hashedPassword}`, 'success');

            } catch (error) {
                console.error('设置密码失败:', error);
                showMessage('❌ 设置密码失败: ' + error.message, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showMessage('❌ 请输入邮箱和密码', 'error');
                return;
            }

            try {
                showMessage('正在验证登录...', 'info');

                // 查询用户
                const user = await supabaseQuery('users', {
                    select: '*',
                    eq: { 
                        email: email,
                        is_active: true
                    },
                    single: true
                });

                if (!user) {
                    showMessage('❌ 用户不存在或已被禁用', 'error');
                    return;
                }

                // 验证密码
                if (!user.password_hash) {
                    showMessage('❌ 用户密码未设置，请点击"重新设置密码"', 'error');
                    return;
                }

                const isPasswordValid = verifyPassword(password, user.password_hash);

                if (isPasswordValid) {
                    showMessage(`✅ 登录成功！<br>
                        欢迎回来，${user.username}！<br>
                        权限级别: ${user.user_type}<br>
                        公司: ${user.company_name || '未填写'}<br><br>
                        🎉 简化认证系统工作正常！`, 'success');
                } else {
                    showMessage('❌ 密码错误', 'error');
                }

            } catch (error) {
                console.error('登录失败:', error);
                showMessage('❌ 登录失败: ' + error.message, 'error');
            }
        }

        // 页面加载时显示状态
        window.addEventListener('load', function() {
            showMessage('✅ 直接API测试页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
