<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终认证系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"], input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .user-status {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 最终认证系统测试</h1>
        
        <div class="user-status" id="userStatus">
            <h3>当前状态：</h3>
            <p id="statusText">检查中...</p>
        </div>
        
        <div class="test-section">
            <h3>登录测试</h3>
            <div class="form-group">
                <label for="loginEmail">邮箱:</label>
                <input type="email" id="loginEmail" value="<EMAIL>" placeholder="输入邮箱">
            </div>
            <div class="form-group">
                <label for="loginPassword">密码:</label>
                <input type="password" id="loginPassword" value="123456" placeholder="输入密码">
            </div>
            <button onclick="testLogin()">测试登录</button>
            <button onclick="logout()" style="background-color: #dc3545;">登出</button>
        </div>
        
        <div class="test-section">
            <h3>注册测试</h3>
            <div class="form-group">
                <label for="regEmail">邮箱:</label>
                <input type="email" id="regEmail" placeholder="输入邮箱">
            </div>
            <div class="form-group">
                <label for="regUsername">用户名:</label>
                <input type="text" id="regUsername" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label for="regPassword">密码:</label>
                <input type="password" id="regPassword" placeholder="输入密码">
            </div>
            <button onclick="testRegister()">测试注册</button>
        </div>
        
        <div class="test-section">
            <h3>权限测试</h3>
            <button onclick="testPermissions()">测试当前用户权限</button>
        </div>
        
        <div id="results"></div>
    </div>

    <!-- 引入脚本 -->
    <script src="supabase-simple.js"></script>
    <script src="js/supabase-config.js"></script>
    
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultElement = document.createElement('div');
            resultElement.className = type;
            resultElement.innerHTML = message;
            resultsDiv.appendChild(resultElement);
            
            // 滚动到结果
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }

        function updateUserStatus() {
            const statusText = document.getElementById('statusText');
            
            if (currentUser) {
                statusText.innerHTML = `
                    <strong>已登录</strong><br>
                    用户名: ${currentUser.username}<br>
                    邮箱: ${currentUser.email}<br>
                    权限: ${getUserTypeDisplay()}<br>
                    公司: ${currentUser.company_name || '未填写'}
                `;
            } else {
                statusText.innerHTML = '<strong>未登录</strong> (游客状态)';
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showResult('请输入邮箱和密码', 'error');
                return;
            }
            
            try {
                showResult(`正在测试登录: ${email}`, 'info');
                
                const result = await loginUser(email, password);
                
                if (result.success) {
                    showResult(`✅ 登录成功！<br>
                        用户: ${result.user.username}<br>
                        权限: ${result.user.user_type}<br>
                        邮箱: ${result.user.email}`, 'success');
                    updateUserStatus();
                } else {
                    showResult(`❌ 登录失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                console.error('登录测试失败:', error);
                showResult(`❌ 登录测试异常: ${error.message}`, 'error');
            }
        }

        async function testRegister() {
            const email = document.getElementById('regEmail').value;
            const username = document.getElementById('regUsername').value;
            const password = document.getElementById('regPassword').value;
            
            if (!email || !username || !password) {
                showResult('请填写所有注册信息', 'error');
                return;
            }
            
            try {
                showResult(`正在测试注册: ${email}`, 'info');
                
                const userData = {
                    email: email,
                    username: username,
                    password: password,
                    userType: 'premium'
                };
                
                const result = await registerUser(userData);
                
                if (result.success) {
                    showResult(`✅ 注册成功！<br>
                        用户: ${result.user.username}<br>
                        邮箱: ${result.user.email}<br>
                        权限: ${result.user.user_type}`, 'success');
                } else {
                    showResult(`❌ 注册失败: ${result.message}`, 'error');
                }
                
            } catch (error) {
                console.error('注册测试失败:', error);
                showResult(`❌ 注册测试异常: ${error.message}`, 'error');
            }
        }

        function logout() {
            const result = logoutUser();
            showResult(`✅ ${result.message}`, 'success');
            updateUserStatus();
        }

        function testPermissions() {
            if (!currentUser) {
                showResult('❌ 请先登录', 'error');
                return;
            }
            
            const permissions = {
                '查看详情': canViewDetails(),
                '下载PDF': canDownload(),
                '下载基础资料': canDownloadBasic()
            };
            
            let permissionText = `<strong>${currentUser.username} 的权限测试结果：</strong><br>`;
            for (const [perm, hasPermission] of Object.entries(permissions)) {
                permissionText += `${perm}: ${hasPermission ? '✅ 有权限' : '❌ 无权限'}<br>`;
            }
            
            showResult(permissionText, 'info');
        }

        // 页面加载时更新状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateUserStatus();
                showResult('✅ 最终认证系统测试页面加载完成', 'info');
                showResult('📋 系统特点：<br>• 只使用 public.users 表认证<br>• 不依赖 Supabase Auth<br>• 管理员可直接修改密码<br>• 支持本地会话管理', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
