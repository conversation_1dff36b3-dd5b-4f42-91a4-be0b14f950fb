<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理（简化版）- 管理后台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 用户管理（简化版）</h1>
        
        <div class="warning">
            <h3>⚠️ 权限问题说明</h3>
            <p>由于Supabase RLS策略限制，正常的用户管理功能可能无法使用。</p>
            <p>这是一个简化版本，尝试绕过权限限制来查看用户数据。</p>
        </div>

        <div>
            <button onclick="loadUsers()">加载用户数据</button>
            <button onclick="testConnection()">测试数据库连接</button>
            <button onclick="checkPermissions()">检查权限</button>
        </div>

        <div id="status"></div>
        <div id="users-container"></div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script>
        // 初始化 Supabase 客户端
        const supabase = window.supabase.createClient(
            'https://snckktsqwrbfwtjlvcfr.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
        );

        function showMessage(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        async function testConnection() {
            try {
                showMessage('正在测试数据库连接...', 'info');
                
                const { data, error } = await supabase
                    .from('users')
                    .select('count', { count: 'exact', head: true });
                
                if (error) {
                    showMessage(`❌ 连接失败: ${error.message}<br>错误代码: ${error.code}`, 'error');
                } else {
                    showMessage(`✅ 连接成功！用户表有 ${data || '未知'} 条记录`, 'success');
                }
            } catch (error) {
                showMessage(`❌ 连接异常: ${error.message}`, 'error');
            }
        }

        async function checkPermissions() {
            showMessage('正在检查权限...', 'info');
            
            const tables = ['users', 'products', 'customer_service_messages'];
            const results = [];
            
            for (const table of tables) {
                try {
                    const { data, error } = await supabase
                        .from(table)
                        .select('*')
                        .limit(1);
                    
                    if (error) {
                        results.push(`❌ ${table}: ${error.message}`);
                    } else {
                        results.push(`✅ ${table}: 可访问`);
                    }
                } catch (e) {
                    results.push(`❌ ${table}: ${e.message}`);
                }
            }
            
            showMessage(`权限检查结果：<br>${results.join('<br>')}`, 'info');
        }

        async function loadUsers() {
            try {
                showMessage('正在加载用户数据...', 'info');
                
                // 尝试多种方式查询用户数据
                let users = null;
                let error = null;
                
                // 方法1: 直接查询
                try {
                    const result = await supabase
                        .from('users')
                        .select('*')
                        .order('created_at', { ascending: false });
                    
                    users = result.data;
                    error = result.error;
                } catch (e) {
                    error = e;
                }
                
                // 方法2: 如果直接查询失败，尝试查询特定字段
                if (error && !users) {
                    try {
                        const result = await supabase
                            .from('users')
                            .select('id, username, email, user_type, created_at')
                            .limit(50);
                        
                        users = result.data;
                        error = result.error;
                    } catch (e) {
                        error = e;
                    }
                }
                
                if (error) {
                    showMessage(`❌ 加载用户失败: ${error.message}<br>错误代码: ${error.code || '未知'}`, 'error');
                    
                    // 显示详细错误信息
                    console.error('详细错误信息:', error);
                    
                    // 如果是权限问题，给出建议
                    if (error.message.includes('permission') || error.message.includes('RLS') || error.code === 'PGRST116') {
                        showMessage(`❌ 权限被拒绝 - 这是RLS策略问题<br>
                            建议解决方案：<br>
                            1. 使用备份工具立即备份数据<br>
                            2. 创建新的Supabase项目<br>
                            3. 正确配置RLS策略`, 'error');
                    }
                    return;
                }
                
                if (!users || users.length === 0) {
                    showMessage('❌ 没有找到用户数据', 'error');
                    return;
                }
                
                // 显示用户数据
                displayUsers(users);
                showMessage(`✅ 成功加载 ${users.length} 个用户`, 'success');
                
            } catch (error) {
                showMessage(`❌ 加载失败: ${error.message}`, 'error');
                console.error('加载用户失败:', error);
            }
        }

        function displayUsers(users) {
            const container = document.getElementById('users-container');
            
            const html = `
                <h3>用户列表 (${users.length} 个用户)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>用户类型</th>
                            <th>状态</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>${user.id || '-'}</td>
                                <td>${user.username || '-'}</td>
                                <td>${user.email || '-'}</td>
                                <td>${user.user_type || '-'}</td>
                                <td>${user.is_active ? '活跃' : '禁用'}</td>
                                <td>${user.created_at ? new Date(user.created_at).toLocaleString() : '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }

        // 页面加载时自动测试连接
        window.addEventListener('load', function() {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>