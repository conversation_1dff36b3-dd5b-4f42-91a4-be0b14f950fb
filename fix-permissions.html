<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复权限</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .step {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid #007bff;
            background: #f8f9fa;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        button {
            padding: 10px 20px;
            margin: 10px 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .admin-btn { background: #28a745; }
        .admin-btn:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <h1>🔧 权限修复工具</h1>
    
    <div class="step">
        <h3>步骤1：选择用户类型</h3>
        <button onclick="setUserType('admin')" class="admin-btn">设为管理员</button>
        <button onclick="setUserType('privileged')">设为特许用户</button>
        <button onclick="setUserType('premium')">设为高级用户</button>
        <button onclick="setUserType('guest')">设为游客</button>
    </div>

    <div class="step">
        <h3>步骤2：检查权限状态</h3>
        <button onclick="checkPermissions()">检查当前权限</button>
        <div id="permission-status"></div>
    </div>

    <div class="step">
        <h3>步骤3：测试产品页面</h3>
        <button onclick="testProductsPage()">测试产品页面访问</button>
        <div id="test-result"></div>
    </div>

    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>

    <script>
        // 直接定义权限系统，不依赖外部文件
        const USER_TYPES = {
            GUEST: 'guest',
            PREMIUM: 'premium',
            PRIVILEGED: 'privileged',
            ADMIN: 'admin'
        };

        // 全局变量
        let currentUser = null;
        let currentUserType = USER_TYPES.GUEST;

        // 权限检查函数
        function canViewDetails() {
            return [USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
        }

        function canDownload() {
            return [USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
        }

        function canDownloadBasic() {
            return [USER_TYPES.PREMIUM, USER_TYPES.PRIVILEGED, USER_TYPES.ADMIN].includes(currentUserType);
        }

        // 等待页面加载
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('权限修复工具已加载');
            console.log('USER_TYPES:', USER_TYPES);
        });

        function setUserType(userType) {
            console.log('设置用户类型为:', userType);

            // 创建模拟用户
            const mockUser = {
                id: 'mock-user-' + Date.now(),
                username: userType === 'admin' ? '管理员' :
                         userType === 'privileged' ? '特许用户' :
                         userType === 'premium' ? '高级用户' : '游客',
                email: userType + '@test.com',
                user_type: userType
            };

            // 设置本地变量
            currentUser = userType === 'guest' ? null : mockUser;
            currentUserType = userType;

            // 强制设置全局变量
            window.currentUser = currentUser;
            window.currentUserType = currentUserType;
            window.USER_TYPES = USER_TYPES;

            // 如果AuthManager存在，也更新它
            if (typeof window.authManager !== 'undefined') {
                window.authManager.currentUser = currentUser;
                window.authManager.userType = userType;
            }

            console.log('权限设置完成:', {
                currentUser: currentUser,
                currentUserType: currentUserType,
                canViewDetails: canViewDetails(),
                canDownload: canDownload(),
                canDownloadBasic: canDownloadBasic()
            });

            alert(`用户类型已设置为: ${userType}\n用户: ${mockUser.username}`);
            checkPermissions();
        }

        function checkPermissions() {
            const statusDiv = document.getElementById('permission-status');

            try {
                const user = currentUser;
                const userType = currentUserType;

                // 检查权限函数
                const canView = canViewDetails();
                const canDownloadPDF = canDownload();
                const canDownloadBasicFiles = canDownloadBasic();

                let html = `
                    <div style="margin-top: 15px;">
                        <strong>当前状态:</strong><br>
                        用户: ${user ? user.username : '未登录'}<br>
                        类型: ${userType}<br>
                        <br>
                        <strong>权限检查:</strong><br>
                        查看详情: ${canView ? '✅ 允许' : '❌ 拒绝'}<br>
                        下载PDF: ${canDownloadPDF ? '✅ 允许' : '❌ 拒绝'}<br>
                        下载基础资料: ${canDownloadBasicFiles ? '✅ 允许' : '❌ 拒绝'}
                    </div>
                `;

                statusDiv.innerHTML = html;
                statusDiv.className = canDownloadPDF ? 'step success' : 'step error';

            } catch (error) {
                statusDiv.innerHTML = `<div style="color: red;">权限检查出错: ${error.message}</div>`;
                statusDiv.className = 'step error';
            }
        }

        function testProductsPage() {
            const resultDiv = document.getElementById('test-result');

            try {
                // 检查是否可以访问产品页面
                const canAccess = canViewDetails();
                
                if (canAccess) {
                    resultDiv.innerHTML = `
                        <div style="margin-top: 15px; color: green;">
                            ✅ 权限正常！可以访问产品页面<br>
                            <a href="products.html" target="_blank" style="color: blue;">点击访问产品页面</a>
                        </div>
                    `;
                    resultDiv.className = 'step success';
                } else {
                    resultDiv.innerHTML = `
                        <div style="margin-top: 15px; color: red;">
                            ❌ 权限不足，无法访问产品页面<br>
                            请先设置为高级用户或以上权限
                        </div>
                    `;
                    resultDiv.className = 'step error';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div style="color: red;">测试出错: ${error.message}</div>`;
                resultDiv.className = 'step error';
            }
        }

        // 页面加载后自动检查
        setTimeout(() => {
            checkPermissions();
        }, 2000);
    </script>
</body>
</html>
