<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能搜索测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-title { font-weight: bold; color: #be131b; margin-bottom: 10px; }
        .test-result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 15px; margin: 5px; background: #be131b; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #d32f2f; }
    </style>
</head>
<body>
    <h1>🧪 智能搜索功能测试</h1>
    
    <div class="test-case">
        <div class="test-title">测试1: 精确匹配 - φ78</div>
        <button onclick="testSearch('φ78', '应该找到顶板(A0001)')">测试 φ78</button>
        <div id="result-1" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试2: 材料搜索 - SPHC</div>
        <button onclick="testSearch('SPHC', '应该找到多个SPHC材料产品')">测试 SPHC</button>
        <div id="result-2" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试3: 厚度搜索 - 2.5</div>
        <button onclick="testSearch('2.5', '应该找到厚度2.5的产品')">测试 2.5</button>
        <div id="result-3" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试4: 近似匹配 - φ75 (误差范围内)</div>
        <button onclick="testSearch('φ75', '应该找到φ78的产品(近似匹配)')">测试 φ75</button>
        <div id="result-4" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试5: 近似匹配 - φ81 (误差范围内)</div>
        <button onclick="testSearch('φ81', '应该找到φ78的产品(近似匹配)')">测试 φ81</button>
        <div id="result-5" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试6: 边界测试 - φ73 (超出误差范围)</div>
        <button onclick="testSearch('φ73', '不应该找到φ78的产品')">测试 φ73</button>
        <div id="result-6" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试7: 规格搜索 - 185</div>
        <button onclick="testSearch('185', '应该找到φ185的弹簧盘')">测试 185</button>
        <div id="result-7" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试8: 误差范围配置 - φ78 (误差±1)</div>
        <button onclick="testSearchWithTolerance('φ75', 1, '误差±1时不应该找到φ78')">测试 φ75 (±1)</button>
        <div id="result-8" class="test-result"></div>
    </div>

    <div class="test-case">
        <div class="test-title">测试9: 误差范围配置 - φ78 (误差±5)</div>
        <button onclick="testSearchWithTolerance('φ73', 5, '误差±5时应该找到φ78')">测试 φ73 (±5)</button>
        <div id="result-9" class="test-result"></div>
    </div>

    <button onclick="runAllTests()" style="background: #28a745; font-size: 16px; padding: 10px 20px;">🚀 运行所有测试</button>

    <!-- 引入必要的脚本 -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/supabase-config.js"></script>
    <script src="js/smart-search.js"></script>

    <script>
        let testSmartSearch;
        let testCounter = 0;

        // 初始化
        async function init() {
            try {
                console.log('初始化智能搜索测试...');
                
                // 创建智能搜索实例
                testSmartSearch = new SmartSearch();

                // 从Supabase加载产品数据
                const { data: products, error } = await supabase
                    .from('products')
                    .select('*');

                if (error) throw error;

                testSmartSearch.setProducts(products);

                console.log('智能搜索初始化完成，产品数量:', testSmartSearch.products.length);

                if (testSmartSearch.products.length === 0) {
                    document.body.innerHTML += '<div class="error">⚠️ 警告：没有加载到产品数据，请检查数据库连接</div>';
                } else {
                    document.body.innerHTML += '<div class="success">✅ 智能搜索初始化成功，已加载 ' + testSmartSearch.products.length + ' 个产品</div>';
                }
                
            } catch (error) {
                console.error('初始化失败:', error);
                document.body.innerHTML += '<div class="error">❌ 初始化失败: ' + error.message + '</div>';
            }
        }

        // 测试带误差范围的搜索
        function testSearchWithTolerance(query, tolerance, expected) {
            // 找到对应的测试结果div
            const buttons = document.querySelectorAll('button');
            let resultDiv = null;

            // 查找包含当前查询的按钮，然后找到对应的结果div
            for (let i = 0; i < buttons.length; i++) {
                if (buttons[i].onclick && buttons[i].onclick.toString().includes(`testSearchWithTolerance('${query}', ${tolerance}`)) {
                    const testCase = buttons[i].closest('.test-case');
                    if (testCase) {
                        resultDiv = testCase.querySelector('.test-result');
                        break;
                    }
                }
            }

            if (!resultDiv) {
                console.error('找不到结果显示区域');
                return;
            }

            if (!testSmartSearch) {
                resultDiv.innerHTML = '<div class="error">❌ 智能搜索未初始化</div>';
                return;
            }

            try {
                console.log(`测试搜索: "${query}" (误差±${tolerance})`);

                const results = testSmartSearch.search(query, {
                    tolerance: tolerance,
                    includeApproximate: true
                });

                const exactCount = results.exact.length;
                const approximateCount = results.approximate.length;
                const totalCount = exactCount + approximateCount;

                let html = `<strong>搜索: "${query}" (误差±${tolerance})</strong><br>`;
                html += `期望: ${expected}<br>`;
                html += `结果: 找到 ${totalCount} 个产品 (精确: ${exactCount}, 近似: ${approximateCount})<br>`;

                if (exactCount > 0) {
                    html += '<br><strong>精确匹配:</strong><br>';
                    results.exact.slice(0, 3).forEach(product => {
                        html += `• ${product.data_id} - ${product.product_name} (${product.specifications || '无规格'})<br>`;
                    });
                }

                if (approximateCount > 0) {
                    html += '<br><strong>近似匹配:</strong><br>';
                    results.approximate.slice(0, 3).forEach(product => {
                        html += `• ${product.data_id} - ${product.product_name} (${product.specifications || '无规格'})<br>`;
                    });
                }

                resultDiv.innerHTML = html;
                resultDiv.className = 'test-result ' + (totalCount > 0 ? 'success' : 'error');

            } catch (error) {
                console.error('搜索测试失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ 搜索失败: ${error.message}</div>`;
            }
        }

        // 测试单个搜索
        function testSearch(query, expected) {
            // 找到对应的测试结果div
            const buttons = document.querySelectorAll('button');
            let resultDiv = null;

            // 查找包含当前查询的按钮，然后找到对应的结果div
            for (let i = 0; i < buttons.length; i++) {
                if (buttons[i].onclick && buttons[i].onclick.toString().includes(`testSearch('${query}'`)) {
                    const testCase = buttons[i].closest('.test-case');
                    if (testCase) {
                        resultDiv = testCase.querySelector('.test-result');
                        break;
                    }
                }
            }

            if (!resultDiv) {
                console.error('找不到结果显示区域');
                return;
            }
            
            if (!testSmartSearch) {
                resultDiv.innerHTML = '<div class="error">❌ 智能搜索未初始化</div>';
                return;
            }

            try {
                console.log(`测试搜索: "${query}"`);

                // 添加调试信息
                console.log('产品数据示例:', testSmartSearch.products.slice(0, 2));

                const results = testSmartSearch.search(query, {
                    tolerance: 3,
                    includeApproximate: true
                });

                console.log('搜索结果:', results);

                const exactCount = results.exact.length;
                const approximateCount = results.approximate.length;
                const totalCount = exactCount + approximateCount;

                let html = `<strong>搜索: "${query}"</strong><br>`;
                html += `期望: ${expected}<br>`;
                html += `结果: 找到 ${totalCount} 个产品 (精确: ${exactCount}, 近似: ${approximateCount})<br>`;

                if (exactCount > 0) {
                    html += '<br><strong>精确匹配:</strong><br>';
                    results.exact.slice(0, 3).forEach(product => {
                        html += `• ${product.data_id} - ${product.product_name} (${product.specifications || '无规格'}, ${product.thickness || '无厚度'}, ${product.material || '无材料'})<br>`;
                    });
                }

                if (approximateCount > 0) {
                    html += '<br><strong>近似匹配:</strong><br>';
                    results.approximate.slice(0, 3).forEach(product => {
                        html += `• ${product.data_id} - ${product.product_name} (${product.specifications || '无规格'}, ${product.thickness || '无厚度'}, ${product.material || '无材料'})<br>`;
                    });
                }

                resultDiv.innerHTML = html;
                resultDiv.className = 'test-result ' + (totalCount > 0 ? 'success' : 'error');

            } catch (error) {
                console.error('搜索测试失败:', error);
                resultDiv.innerHTML = `<div class="error">❌ 搜索失败: ${error.message}</div>`;
            }
        }

        // 运行所有测试
        function runAllTests() {
            const tests = [
                ['φ78', '应该找到顶板(A0001)'],
                ['SPHC', '应该找到多个SPHC材料产品'],
                ['2.5', '应该找到厚度2.5的产品'],
                ['φ75', '应该找到φ78的产品(近似匹配)'],
                ['φ81', '应该找到φ78的产品(近似匹配)'],
                ['φ73', '不应该找到φ78的产品'],
                ['185', '应该找到φ185的弹簧盘']
            ];

            const toleranceTests = [
                ['φ75', 1, '误差±1时不应该找到φ78'],
                ['φ73', 5, '误差±5时应该找到φ78']
            ];

            testCounter = 0;

            // 运行普通测试
            tests.forEach(([query, expected]) => {
                setTimeout(() => testSearch(query, expected), testCounter * 500);
            });

            // 运行误差范围测试
            toleranceTests.forEach(([query, tolerance, expected]) => {
                setTimeout(() => testSearchWithTolerance(query, tolerance, expected), (testCounter + tests.length) * 500);
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
